import BuildTarget.stackratePlatform
import com.lightbend.lagom.core.LagomVersion
import com.typesafe.sbt.packager.Keys.linuxStartScriptTemplate
import com.typesafe.sbt.packager.SettingsHelper.makeDeploymentSettings
import com.typesafe.sbt.packager.debian.DebianPlugin.autoImport.debianPackageDependencies
import sbt.Keys.{libraryDependencies, publishTo}

ThisBuild / organization := "de.fellows"
ThisBuild / version      := stackratePlatform

ThisBuild / jibLabels := Map(
  "maintainer" -> "<EMAIL>",
  "version"    -> s"${version.value}"
)
val jib_base_image = sys.env.getOrElse("JIB_BASE_IMAGE", "eclipse-temurin:17-jre-focal")

val jib_registry           = sys.env.getOrElse("IMAGE_REGISTRY", "lumiquote.azurecr.io")
val repository_shared_path = sys.env.getOrElse("IMAGE_SHARED_PATH", "images/stackrate/")

// the Scala version that will be used for cross-compiled libraries
ThisBuild / scalaVersion := "2.13.15"
scalaVersion             := "2.13.15"
Universal / javaOptions += "--Dpidfile.path=/dev/null"
ThisBuild / javaOptions += "-Dplay.server.http.idleTimeout=11s"
ThisBuild / javaOptions += "--add-opens=java.desktop/java.awt.geom=ALL-UNNAMED"
ThisBuild / javaOptions += "--add-exports=java.desktop/sun.awt.geom=ALL-UNNAMED"

ThisBuild / Test / fork := true

ThisBuild / scalacOptions ++= Seq(
  "-language:postfixOps",
  "-language:implicitConversions",
  "-language:experimental.macros",
  "-Wconf:cat=deprecation:ws,cat=feature:ws,cat=optimizer:ws,cat=lint:e"
)

useCoursier := false

ThisBuild / dependencyOverrides ++= Seq(
  "com.squareup.okhttp3" % "okhttp" % "4.9.1"
)

ThisBuild / libraryDependencySchemes +=
  "org.scala-lang.modules" %% "scala-java8-compat" % VersionScheme.Always

ThisBuild / lagomServiceGatewayPort := 9010

val akkaVersion = LagomVersion.akka
val playVersion = LagomVersion.play

val macwire         = "com.softwaremill.macwire" %% "macros"                  % "2.5.6"    % "provided"
val scalaTest       = "org.scalatest"            %% "scalatest"               % "3.2.19"   % Test
val scalaTestPlus   = "org.scalatestplus"        %% "scalacheck-1-18"         % "3.2.19.0" % Test
val cassandraExtras = "com.datastax.cassandra"    % "cassandra-driver-extras" % "3.11.1"
val jwt             = "com.pauldijou"            %% "jwt-play-json"           % "3.2.0"

val clusterdiscovery = Seq(
  "com.lightbend.akka.discovery" %% "akka-discovery-kubernetes-api" % "1.1.3",
  "com.typesafe.akka"            %% "akka-cluster"                  % akkaVersion,
  "com.typesafe.akka"            %% "akka-discovery"                % akkaVersion
)

val apacheHttpComponents = Seq(
  "org.apache.httpcomponents" % "httpclient" % "4.5.14"
)

val redis = Seq(
  "redis.clients" % "jedis" % "5.1.2"
)

val svixDependencies = Seq(
  "com.svix" % "svix" % "0.63.1"
)

val lagomOpenapiVersion = "1.2.0"
val swaggerAnnotations  = "io.swagger.core.v3" % "swagger-annotations"      % "2.2.0"
val lagomOpenapiApi     = "org.taymyr.lagom"  %% "lagom-openapi-scala-api"  % lagomOpenapiVersion
val lagomOpenapiImpl    = "org.taymyr.lagom"  %% "lagom-openapi-scala-impl" % lagomOpenapiVersion

val batik = Seq(
  "org.apache.xmlgraphics" % "batik-awt-util"   % "1.14",
  "org.apache.xmlgraphics" % "batik-svggen"     % "1.14",
  "org.apache.xmlgraphics" % "batik-svg-dom"    % "1.14",
  "org.apache.xmlgraphics" % "batik-ext"        % "1.14",
  "org.apache.xmlgraphics" % "batik-anim"       % "1.14",
  "org.apache.xmlgraphics" % "batik-transcoder" % "1.14",
  "org.apache.xmlgraphics" % "batik-codec"      % "1.14",
  "org.apache.commons"     % "commons-math3"    % "3.6.1",
  "org.locationtech.jts"   % "jts"              % "1.18.2",
  "org.locationtech.jts"   % "jts-core"         % "1.18.2",
  "org.locationtech.jts"   % "jts-io"           % "1.18.2",
  "org.locationtech.jts"   % "jts-modules"      % "1.18.2"
)

val enumeratumVersion = "1.7.0"
val enumeratum = Seq(
  "com.beachape" %% "enumeratum"           % enumeratumVersion,
  "com.beachape" %% "enumeratum-play-json" % enumeratumVersion
)

val jsonSchemaVersion = "0.7.8"
val jsonSchema = Seq(
  "com.github.andyglow" %% "scala-jsonschema"           % jsonSchemaVersion,
  "com.github.andyglow" %% "scala-jsonschema-play-json" % jsonSchemaVersion
)

ThisBuild / lagomKafkaEnabled     := false
ThisBuild / lagomKafkaAddress     := "localhost:9092"
ThisBuild / lagomCassandraEnabled := false

dependencyOverrides ++= Seq(
  "com.typesafe.akka" %% "akka-actor"                  % akkaVersion,
  "com.typesafe.akka" %% "akka-remote"                 % akkaVersion,
  "com.typesafe.akka" %% "akka-cluster"                % akkaVersion,
  "com.typesafe.akka" %% "akka-cluster-sharding"       % akkaVersion,
  "com.typesafe.akka" %% "akka-cluster-sharding-typed" % akkaVersion,
  "com.typesafe.akka" %% "akka-cluster-tools"          % akkaVersion,
  "com.typesafe.akka" %% "akka-cluster-typed"          % akkaVersion,
  "com.typesafe.akka" %% "akka-coordination"           % akkaVersion,
  "com.typesafe.akka" %% "akka-discovery"              % akkaVersion,
  "com.typesafe.akka" %% "akka-distributed-data"       % akkaVersion,
  "com.typesafe.akka" %% "akka-serialization-jackson"  % akkaVersion,
  "com.typesafe.akka" %% "akka-persistence"            % akkaVersion,
  "com.typesafe.akka" %% "akka-persistence-query"      % akkaVersion,
  "com.typesafe.akka" %% "akka-slf4j"                  % akkaVersion,
  "com.typesafe.akka" %% "akka-stream"                 % akkaVersion,
  "com.typesafe.akka" %% "akka-protobuf-v3"            % akkaVersion,
  "com.typesafe.akka" %% "akka-actor-typed"            % akkaVersion,
  "com.typesafe.akka" %% "akka-persistence-typed"      % akkaVersion,
  "com.typesafe.akka" %% "akka-multi-node-testkit"     % akkaVersion % Test,
  "com.typesafe.akka" %% "akka-testkit"                % akkaVersion % Test,
  "com.typesafe.akka" %% "akka-stream-testkit"         % akkaVersion % Test,
  "com.typesafe.akka" %% "akka-actor-testkit-typed"    % akkaVersion % Test
  // Use "sbt-dependency-graph" or any other dependency report generator to
  // make sure you add all the necessary dependencies on this list
)

val utilsDep = utils % "compile->compile;test->test"

val segmentLib = Seq(
  "com.segment.analytics.java" % "analytics" % "3.4.0"
)

val telemetryVersion      = "1.13.0"
val telemetryAgentVersion = "1.13.0-alpha"
val telemetryApi = Seq(
  "io.opentelemetry" % "opentelemetry-bom" % telemetryVersion,
  "io.opentelemetry" % "opentelemetry-api" % telemetryVersion,
  "io.opentelemetry" % "opentelemetry-sdk" % telemetryVersion
)
val telemetrySdk = telemetryApi ++ Seq(
  "io.opentelemetry"                 % "opentelemetry-extension-annotations"       % telemetryVersion,
  "io.opentelemetry"                 % "opentelemetry-sdk-extension-autoconfigure" % telemetryAgentVersion,
  "io.opentelemetry"                 % "opentelemetry-exporter-otlp"               % telemetryVersion,
  "io.opentelemetry"                 % "opentelemetry-exporter-otlp-http-trace"    % telemetryVersion,
  "io.opentelemetry"                 % "opentelemetry-exporters-otlp"              % "0.9.1",
  "io.opentelemetry.instrumentation" % "opentelemetry-java-http-client"            % "1.29.0-alpha",
  "io.opentelemetry.javaagent"       % "opentelemetry-javaagent"                   % telemetryVersion,
  "io.grpc"                          % "grpc-okhttp"                               % "1.30.2"
)

val kanelaAgent  = "io.kamon" % "kanela-agent" % "1.0.17"
val kamonVersion = "2.6.0"
val kamon = Seq(
  "io.kamon" %% "kamon-bundle"        % kamonVersion,
  "io.kamon" %% "kamon-opentelemetry" % kamonVersion
) ++ Seq(kanelaAgent)

val logback = "ch.qos.logback" % "logback-classic" % "1.2.11"

dockerUsername     := Some("sam")
dockerUpdateLatest := true

ThisBuild / publishTo := Some(
  ("Stackrate Snapshots Nexus" at "http://jenkins.electronic-fellows.de/nexus/repository/stackrate-snapshots/").withAllowInsecureProtocol(
    true
  )
)

resolvers += Resolver.sonatypeRepo("releases")

makeDeploymentSettings(Debian, Debian / packageBin, "deb")

ThisBuild / lagomUnmanagedServices := Map(
  "mailgun"    -> "https://api.eu.mailgun.net",
  "camunda"    -> "http://localhost:8080/rest",
  "cas_native" -> "tcp://localhost:9042"
)

val servicePlugins = Seq(
  LagomScala,
  StackratePlugin,
  DebianPlugin,
  SystemdPlugin,
  JavaServerAppPackaging
  //  JavaServerAppPackaging
)

lazy val commonServiceSettings = Seq(
  dockerRepository                  := Some(BuildTarget.dockerRepository),
  dockerUpdateLatest                := true,
  Debian / linuxStartScriptTemplate := ((ThisBuild / baseDirectory).value / "templates" / "systemd-template").asURL,
  maintainer                        := "Samuel Leisering <<EMAIL>>",
  packageSummary                    := s"Stackrate ${name.value}",
  packageDescription                := s"""A Stackrate Package: ${name.value}""",
  packageName                       := s"stackrate-${name.value}",
  daemonUser                        := "stackrate",
  daemonGroup                       := "stackrate",
  fileDescriptorLimit               := Some("65536"),
  Debian / version := {
    val build = sys.env.get("CHANGE_ID").orElse(sys.env.get("BUILD_NUMBER")).getOrElse("0")
    version.value.replace("SNAPSHOT", s"build-$build")
  },
  isSnapshot := true,

  // mirrord
  fork                 := true,
  lagomServiceHttpPort := 9000, // local port should be the same as the remote port
  testForkedParallel   := true
)

val commonServiceDependencies = Seq(
  lagomScaladslPersistenceCassandra,
  lagomScaladslKafkaBroker,
  lagomScaladslTestKit,
  macwire
) ++ clusterdiscovery ++ kamon ++ Seq(kanelaAgent) ++ telemetrySdk

lazy val `root` = (project in file("."))
  .settings(
    name                      := "aggregate-project",
    jibImageBuild / aggregate := false
  )
  .enablePlugins(PlayScala)
  .disablePlugins(JibPlugin)
  .aggregate(
    `analysis-api`,
    `analysis-impl`,
    `assembly-api`,
    `assembly-commons`,
    `assembly-impl`,
    `camunda-api`,
    `camunda-bridge-api`,
    `camunda-bridge-impl`,
    `collaboration-api`,
    `collaboration-impl`,
    `customer-api`,
    `customer-impl`,
    `dfm-api`,
    `dfm-impl`,
    `entity-utils`,
    `erpnext-api`,
    `erpnext-bridge-api`,
    `erpnext-bridge-impl`,
    `gerber-parser`,
    `http`,
    `impl-utils`,
    `inbox-api`,
    `inbox-commons`,
    `inbox-impl`,
    `ki-matcher`,
    `layerstack-api`,
    `layerstack-common`,
    `layerstack-impl`,
    `luminovo-model`,
    `mailgun`,
    `native-converter-api`,
    `native-converter-impl`,
    `native-converter-pylib`,
    `notification-api`,
    `notification-impl`,
    `odb-parser`,
    `panel-api`,
    `panel-impl`,
    `pcb-api`,
    `pcb-impl`,
    `pcb-model`,
    `pcb-server`,
    `price-api`,
    `price-impl`,
    `profile-api`,
    `profile-impl`,
    `quotation-api`,
    `quotation-impl`,
    `redislog`,
    `renderer-api`,
    `renderer-impl`,
    `security-api`,
    `security-impl`,
    `supplier-api`,
    `supplier-impl`,
    `svix`,
    `templating`,
    `user-api`,
    `user-impl`,
    `utils`
  )

lazy val `services` = project
  .settings(
    name := "aggregate-project"
  )
  .enablePlugins(PlayScala)
  .disablePlugins(JibPlugin)
  .aggregate(
    `user-impl`,
    `customer-impl`,
    `profile-impl`,
    `security-impl`,
    `collaboration-impl`,
    `notification-impl`,
    `pcb-impl`,
    `assembly-impl`,
    `renderer-impl`,
    //    `erpnext-bridge-impl`,
    `supplier-impl`,
    `camunda-bridge-impl`,
    `quotation-impl`,
    `inbox-impl`,
    `layerstack-impl`,
    `panel-impl`,
    `price-impl`,
    `dfm-impl`,
    `analysis-impl`,
    `native-converter-impl`,
    `pcb-server`
  )

val PcbServerAkkaVersion     = "2.6.19"
val PcbServerAkkaHttpVersion = "10.2.10"

val sttpVersion = "3.8.15"

lazy val scalaXml    = "org.scala-lang.modules" %% "scala-xml"                % "1.3.0"
lazy val scalaParser = "org.scala-lang.modules" %% "scala-parser-combinators" % "1.1.2"
lazy val jaxbApi     = "javax.xml.bind"          % "jaxb-api"                 % "2.3.0"
lazy val dispatchV   = "1.1.3"
lazy val dispatch    = "org.dispatchhttp"       %% "dispatch-core"            % dispatchV

lazy val commonsCompressVersion = "1.21"

lazy val `pcb-server` = (project in file("microservices/pcb"))
  .enablePlugins(ScalaxbPlugin)
  .settings(
    scalacOptions ++= Seq("-Xlint:unused", "-Ywarn-macros:after"),
    fork                                       := true,
    testForkedParallel                         := true,
    name                                       := "pcb-server",
    version                                    := sys.env.getOrElse("PCB_SERVER_VERSION", stackratePlatform),
    jibRegistry                                := jib_registry,
    jibBaseImage                               := jib_base_image,
    jibCustomRepositoryPath                    := Some(repository_shared_path + "pcb-server"),
    Compile / scalaxb / scalaxbDispatchVersion := dispatchV,
    Compile / scalaxb / scalaxbPackageName     := "de.fellows.generated.integrations.apct",
    libraryDependencies ++= Seq(
      "com.typesafe.akka"             %% "akka-actor-testkit-typed"    % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-cluster"                % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-cluster-sharding"       % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-cluster-sharding-typed" % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-cluster-tools"          % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-cluster-typed"          % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-coordination"           % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-discovery"              % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-distributed-data"       % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-persistence"            % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-persistence-query"      % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-persistence-typed"      % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-pki"                    % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-remote"                 % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-serialization-jackson"  % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-stream-typed"           % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-testkit"                % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-actor-typed"            % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-stream"                 % PcbServerAkkaVersion,
      "com.typesafe.akka"             %% "akka-http-spray-json"        % PcbServerAkkaHttpVersion,
      "com.typesafe.akka"             %% "akka-http"                   % PcbServerAkkaHttpVersion,
      "ch.megard"                     %% "akka-http-cors"              % "1.2.0",
      "com.typesafe.play"             %% "play-json"                   % "2.9.2",
      "com.softwaremill.sttp.client3" %% "core"                        % sttpVersion,
      "com.softwaremill.sttp.client3" %% "play-json"                   % sttpVersion,
      "com.softwaremill.sttp.client3" %% "slf4j-backend"               % sttpVersion,
      "dev.zio"                       %% "zio-prelude"                 % "1.0.0-RC14",
      "org.typelevel"                 %% "cats-core"                   % "2.9.0",
      "com.osinka.i18n"               %% "scala-i18n"                  % "1.0.3",
      "org.apache.commons"             % "commons-compress"            % commonsCompressVersion,
      "com.github.tototoshi"          %% "scala-csv"                   % "1.3.10"   % Test,
      "org.scalatest"                 %% "scalatest"                   % "3.2.19"   % Test,
      "org.scalatestplus"             %% "scalacheck-1-18"             % "3.2.19.0" % Test,
      logback
    ) ++ enumeratum ++ batik ++ telemetrySdk ++ Seq(dispatch, scalaXml, scalaParser, jaxbApi)
  ).dependsOn(`pcb-api`, `price-api`, `quotation-api`, `customer-api`, `user-api`, `templating`, `luminovo-model`)

lazy val `http` = (project in file("common/http"))
  .settings(
    version := sys.env.getOrElse("HTTP_VERSION", stackratePlatform),
    name    := "http",
    libraryDependencies ++= Seq(
    )
  )

lazy val `user-api` = (project in file("framework/user/api"))
  .settings(
    name    := "user-api",
    version := sys.env.getOrElse("USER_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `notification-common`)

val wkhtmlDependencies = Seq(
  "wkhtmltopdf",
  "xvfb",
  "xauth"
)
lazy val `user-impl` = (project in file("framework/user/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "user-impl",
    version                 := sys.env.getOrElse("USER_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    Debian / debianPackageDependencies ++= wkhtmlDependencies,
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `user-api`,
    `security-api`,
    `profile-api`,
    `templating`,
    utilsDep,
    `impl-utils`,
    mailgun,
    `notification-common`
  )

lazy val `customer-api` = (project in file("framework/customer/api"))
  .settings(
    name    := "customer-api",
    version := sys.env.getOrElse("CUSTOMER_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `notification-common`, `erpnext-bridge-api`)

lazy val `customer-impl` = (project in file("framework/customer/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "customer-impl",
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    version                 := sys.env.getOrElse("CUSTOMER_IMPL_VERSION", stackratePlatform),
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(`customer-api`, `security-api`, utilsDep, `notification-common`, `impl-utils`, `erpnext-bridge-api`)

lazy val `profile-api` = (project in file("framework/profile/api"))
  .settings(
    name    := "profile-api",
    version := sys.env.getOrElse("PROFILE_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils)

lazy val `profile-impl` = (project in file("framework/profile/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "profile-impl",
    version                 := sys.env.getOrElse("PROFILE_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras,
      "org.apache.commons" % "commons-imaging" % "1.0-alpha2"
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(`profile-api`, `user-api`, `security-api`, `impl-utils`, utilsDep)

lazy val svix = (project in file("common/svix"))
  .settings(
    version := sys.env.getOrElse("SVIX_VERSION", stackratePlatform),
    name    := "svix",
    libraryDependencies ++= Seq(
      "com.typesafe.play" %% "play" % playVersion
    ) ++ jsonSchema ++ svixDependencies ++ enumeratum
  )

lazy val `utils` = (project in file("common/utils"))
  .settings(
    version := sys.env.getOrElse("UTILS_VERSION", stackratePlatform),
    name    := "utils",
    libraryDependencies ++= Seq(
      lagomScaladslServer,
      lagomScaladslTestKit,
      jwt,
      lagomScaladslAkkaDiscovery,
      scalaTest,
      scalaTestPlus,
      swaggerAnnotations,
      logback
    ) ++ enumeratum ++ jsonSchema ++ kamon ++ redis ++ apacheHttpComponents
  )

lazy val `luminovo-model` = (project in file("common/luminovo-model"))
  .settings(
    version := sys.env.getOrElse("LUMINOVO_MODEL_VERSION", stackratePlatform),
    name    := "luminovo-model",
    libraryDependencies ++= Seq(
      scalaTest,
      scalaTestPlus,
      "com.typesafe.play" %% "play" % playVersion
    )
  )
  .dependsOn(`utils`)

lazy val `templating` = (project in file("common/templating"))
  .settings(
    version := sys.env.getOrElse("TEMPLATING_VERSION", stackratePlatform),
    name    := "templating",
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      // servicelocator,
      scalaTest,
      "com.hubspot.jinjava" % "jinjava"              % "2.6.0",
      "org.apache.pdfbox"   % "pdfbox-tools"         % "2.0.25",
      "net.sf.cssbox"       % "pdf2dom"              % "1.9",
      "com.openhtmltopdf"   % "openhtmltopdf-core"   % "1.0.10",
      "com.openhtmltopdf"   % "openhtmltopdf-pdfbox" % "1.0.10",
      "com.openhtmltopdf"   % "openhtmltopdf-slf4j"  % "1.0.10",
      logback
    )
  )

lazy val `odb-parser` = (project in file("common/odb"))
  .settings(
    version := sys.env.getOrElse("IMPL_UTILS_VERSION", stackratePlatform),
    name    := "odb-parser",
    libraryDependencies ++= Seq(
      scalaTest
    )
  )

lazy val `impl-utils` = (project in file("common/impl-utils"))
  .settings(
    version := sys.env.getOrElse("IMPL_UTILS_VERSION", stackratePlatform),
    name    := "impl-utils",
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      lagomScaladslTestKit,
      lagomScaladslPersistenceCassandra,
      lagomScaladslKafkaBroker,
      // servicelocator,
      scalaTest,
      cassandraExtras,
      lagomOpenapiImpl,
      "com.typesafe.akka" %% "akka-serialization-jackson" % akkaVersion
    ) ++ segmentLib
  ).dependsOn(utils, `security-api`)

lazy val `entity-utils` = (project in file("common/entity-utils"))
  .settings(
    version := sys.env.getOrElse("ENTITY_UTILS_VERSION", stackratePlatform),
    name    := "entity-utils",
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      lagomScaladslTestKit,
      lagomScaladslPersistenceCassandra,
      jwt,
      // servicelocator,
      scalaTest,
      cassandraExtras
    )
  )

lazy val `redislog` = (project in file("common/redislog"))
  .settings(
    version := sys.env.getOrElse("REDISLOG_VERSION", stackratePlatform),
    name    := "redislog",
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      lagomScaladslPersistence,
      jwt
    )
  ).dependsOn(utils)

lazy val mailgun = (project in file("common/mailgun"))
  .settings(
    version := sys.env.getOrElse("MAILGUN_VERSION", stackratePlatform),
    name    := "mailgun",
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  )
  .dependsOn(utils, `impl-utils`)

lazy val `security-api` = (project in file("framework/security/api"))
  .settings(
    name    := "security-api",
    version := sys.env.getOrElse("SECURITY_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utilsDep)

lazy val `security-impl` = (project in file("framework/security/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "security-impl",
    version                 := sys.env.getOrElse("SECURITY_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(`security-api`, `user-api`, utilsDep, `impl-utils`)

lazy val `collaboration-api` = (project in file("framework/collaboration/api"))
  .settings(
    name    := "collaboration-api",
    version := sys.env.getOrElse("COLLABORATION_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utilsDep, `assembly-api`, `assembly-commons`, `user-api`)

lazy val `collaboration-impl` = (project in file("framework/collaboration/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "collaboration-impl",
    version                 := sys.env.getOrElse("COLLABORATION_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `collaboration-api`,
    `user-api`,
    `security-api`,
    `assembly-api`,
    `assembly-commons`,
    `profile-api`,
    `pcb-api`,
    `impl-utils`,
    utilsDep
  )

lazy val `notification-common` = (project in file("framework/notification/common"))
  .settings(
    name    := "notification-common",
    version := sys.env.getOrElse("NOTIFICATION_COMMON_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      lagomScaladslTestKit,
      // servicelocator,
      scalaTest
    )
  ).dependsOn(utilsDep)

lazy val `notification-api` = (project in file("framework/notification/api"))
  .settings(
    name    := "notification-api",
    version := sys.env.getOrElse("NOTIFICATION_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utilsDep, `notification-common`)

lazy val `camunda-api` = (project in file("framework/camunda/api"))
  .settings(
    name    := "camunda-api",
    version := sys.env.getOrElse("CAMUNDA_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utilsDep, `assembly-api`)

lazy val `erpnext-api` = (project in file("framework/erpnext/api"))
  .settings(
    name    := "erpnext-api",
    version := sys.env.getOrElse("ERPNEXT_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utilsDep)

lazy val `erpnext-bridge-api` = (project in file("framework/erpnext-bridge/api"))
  .settings(
    name    := "erpnext-bridge-api",
    version := sys.env.getOrElse("ERPNEXT_BRIDGE_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      "org.scalaj" %% "scalaj-http" % "2.4.2"
    )
  ).dependsOn(utilsDep, `erpnext-api`)

lazy val `erpnext-bridge-impl` = (project in file("framework/erpnext-bridge/impl"))
  .enablePlugins(LagomAkkaHttpServer)
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "erpnext-bridge-impl",
    version                 := sys.env.getOrElse("ERPNEXT_BRIDGE_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(utilsDep, `erpnext-bridge-api`, `impl-utils`, `erpnext-api`)

lazy val `notification-impl` = (project in file("framework/notification/impl"))
  .enablePlugins(LagomAkkaHttpServer)
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "notification-impl",
    version                 := sys.env.getOrElse("NOTIFICATION_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `security-api`,
    `notification-api`,
    utilsDep,
    `notification-common`,
    `user-api`,
    `profile-api`,
    `impl-utils`,
    `mailgun`
  )

lazy val `assembly-api` = (project in file("framework/assembly/api"))
  .settings(
    name    := "assembly-api",
    version := sys.env.getOrElse("ASSEMBLY_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `inbox-commons`)

lazy val `assembly-commons` = (project in file("framework/assembly/commons"))
  .settings(
    name    := "assembly-commons",
    version := sys.env.getOrElse("ASSEMBLY_COMMONS_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslPersistenceCassandra,
      lagomScaladslApi,
      lagomScaladslServer,
      cassandraExtras,
      scalaTest
    )
  ).dependsOn(`impl-utils`, utils)

lazy val `assembly-impl` = (project in file("framework/assembly/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "assembly-impl",
    version                 := sys.env.getOrElse("ASSEMBLY_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras,
      "com.github.junrar"  % "junrar"           % "4.0.0",
      "org.tukaani"        % "xz"               % "1.9",
      "org.apache.commons" % "commons-compress" % commonsCompressVersion
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `assembly-api`,
    `assembly-commons`,
    `pcb-api`,
    `customer-api`,
    `security-api`,
    utilsDep,
    `entity-utils`,
    `inbox-api`,
    `impl-utils`,
    `redislog`,
    svix
  )

lazy val `pcb-api` = (project in file("ems/pcb/api"))
  .settings(
    name    := "pcb-api",
    version := sys.env.getOrElse("PCB_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      scalaTest,
      scalaTestPlus,
      lagomScaladslApi
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`, `layerstack-api`, `panel-api`, `dfm-api`)

lazy val `pcb-model` = (project in file("ems/pcb/model"))
  .settings(
    name    := "pcb-model",
    version := sys.env.getOrElse("PCB_MODEL_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslPersistenceCassandra,
      scalaTest,
      cassandraExtras,
      "org.apache.xmlgraphics"  % "batik-awt-util"             % "1.14",
      "math.geom2d"             % "javaGeom"                   % "0.11.1",
      "org.scala-lang.modules" %% "scala-parallel-collections" % "0.2.0",
      "com.pump" %% "pumpernickel" % "1.03" from "https://github.com/mickleness/pumpernickel/raw/master/release/jars/1.03/Pumpernickel.jar"
    )
  ).dependsOn(`impl-utils`, `assembly-commons`, `odb-parser`)

lazy val `pcb-impl` = (project in file("ems/pcb/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "pcb-impl",
    version                 := sys.env.getOrElse("PCB_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `pcb-api`,
    `gerber-parser`,
    `pcb-model`,
    `assembly-api`,
    `security-api`,
    `renderer-api`,
    `camunda-api`,
    `camunda-bridge-api`,
    `utilsDep`,
    `user-api`,
    `impl-utils`,
    `odb-parser`,
    `redislog`,
    svix
  )

lazy val `renderer-api` = (project in file("ems/renderer/api"))
  .settings(
    name    := "renderer-api",
    version := sys.env.getOrElse("RENDERER_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(
    utils,
    `assembly-commons`,
    `pcb-model`,
    `assembly-api`,
    `native-converter-api`,
    `layerstack-common`,
    `redislog`
  )

lazy val `ki-matcher` = (project in file("ems/renderer/ki"))
  .settings(
    name    := "kimatcher",
    version := sys.env.getOrElse("KI_MATCHER_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      "org.tensorflow" % "tensorflow" % "1.15.0",
      "org.tensorflow" % "proto"      % "1.15.0"
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`, `renderer-api`)

lazy val `renderer-impl` = (project in file("ems/renderer/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "renderer-impl",
    version                 := sys.env.getOrElse("RENDERER_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    Debian / debianPackageDependencies ++= Seq("webp"),
    libraryDependencies ++= Seq(
      filters,
      scalaTest,
      cassandraExtras,
      "org.scala-lang.modules" %% "scala-parallel-collections" % "0.2.0",
      "org.openpnp"             % "opencv"                     % "3.4.2-2"
    ) ++ batik ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `renderer-api`,
    `gerber-parser`,
    `pcb-model`,
    `assembly-api`,
    `pcb-api`,
    `security-api`,
    `layerstack-api`,
    `camunda-api`,
    `impl-utils`,
    `camunda-bridge-api`,
    `camunda-api`,
    utilsDep,
    `ki-matcher`,
    `odb-parser`,
    `redislog`,
    svix,
    `layerstack-impl` % Test,
    `pcb-impl`        % Test
  )

lazy val `layerstack-common` = (project in file("ems/layerstack/common"))
  .settings(
    name    := "layerstack-common",
    version := sys.env.getOrElse("LAYERSTACK_COMMON_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`)

lazy val `layerstack-api` = (project in file("ems/layerstack/api"))
  .settings(
    name    := "layerstack-api",
    version := sys.env.getOrElse("LAYERSTACK_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`, `layerstack-common`)

lazy val `layerstack-impl` = (project in file("ems/layerstack/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "layerstack-impl",
    version                 := sys.env.getOrElse("LAYERSTACK_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras,
      "org.scalaj"        %% "scalaj-http"      % "2.4.2",
      "org.apache.commons" % "commons-compress" % commonsCompressVersion
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `layerstack-api`,
    `gerber-parser`,
    `pcb-model`,
    `assembly-api`,
    `pcb-api`,
    `security-api`,
    `user-api`,
    utilsDep,
    `impl-utils`,
    `pcb-impl` % Test
  )

lazy val `gerber-parser` = (project in file("ems/pcb/gerber-parser"))
  // .enablePlugins(Antlr4Plugin)
  .settings(
    name    := "gerber-parser",
    version := sys.env.getOrElse("GERBER_PARSER_VERSION", stackratePlatform),
    // Antlr4 / antlr4PackageName := Some("de.fellows.ems.gerber.parser"),
    // Antlr4 / antlr4Version     := "4.13.1",
    // Antlr4 / antlr4GenVisitor  := true,
    libraryDependencies ++= Seq(
      "org.antlr" % "antlr4-runtime" % "4.13.1",
      "org.antlr" % "stringtemplate" % "4.0.2"
    )
  )

lazy val `supplier-api` = (project in file("ems/pcb-supplier/api"))
  .settings(
    name    := "pcb-supplier-api",
    version := sys.env.getOrElse("SUPPLIER_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`, `quotation-api`, `utils`)

lazy val `supplier-impl` = (project in file("ems/pcb-supplier/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "pcb-supplier-impl",
    version                 := sys.env.getOrElse("SUPPLIER_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(`supplier-api`, `pcb-api`, `security-api`, `impl-utils`, utilsDep)

lazy val `dfm-api` = (project in file("ems/dfm/api"))
  .settings(
    name    := "dfm-api",
    version := sys.env.getOrElse("DFM_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `supplier-api`, `assembly-commons`, `pcb-model`, `renderer-api`, `utils`)

lazy val `dfm-impl` = (project in file("ems/dfm/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "dfm-impl",
    version                 := sys.env.getOrElse("DFM_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `supplier-api`,
    `pcb-api`,
    `renderer-api`,
    `dfm-api`,
    `security-api`,
    `impl-utils`,
    `layerstack-api`,
    utilsDep
  )

lazy val `camunda-bridge-api` = (project in file("framework/camunda-bridge/api"))
  .settings(
    name    := "camunda-bridge-api",
    version := sys.env.getOrElse("CAMUNDA_BRIDGE_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`, `camunda-api`)

lazy val `camunda-bridge-impl` = (project in file("framework/camunda-bridge/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "camunda-bridge-impl",
    version                 := sys.env.getOrElse("CAMUNDA_BRIDGE_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `camunda-bridge-api`,
    `camunda-api`,
    `supplier-api`,
    `assembly-api`,
    `security-api`,
    `pcb-api`,
    `renderer-api`,
    `inbox-api`,
    `layerstack-api`,
    `user-api`,
    `impl-utils`,
    utilsDep
  )

lazy val `quotation-api` = (project in file("framework/quotation/api"))
  .settings(
    name    := "quotation-api",
    version := sys.env.getOrElse("QUOTATION_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `assembly-api`, `pcb-model`)
//
//
//
lazy val `quotation-impl` = (project in file("framework/quotation/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "quotation-impl",
    version                 := sys.env.getOrElse("QUOTATION_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    Debian / debianPackageDependencies ++= wkhtmlDependencies,
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `quotation-api`,
    `supplier-api`,
    `security-api`,
    `customer-api`,
    `assembly-api`,
    utilsDep,
    `templating`,
    `pcb-api`,
    `pcb-model`,
    `assembly-api`,
    `assembly-commons`,
    `user-api`,
    `profile-api`,
    `impl-utils`,
    `mailgun`,
    `inbox-api`
  )

lazy val `price-api` = (project in file("framework/price/api"))
  .settings(
    name    := "price-api",
    version := sys.env.getOrElse("PRICE_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer,
      swaggerAnnotations
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-api`, `pcb-model`, `supplier-api`)
//
//
//
lazy val `price-impl` = (project in file("framework/price/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "price-impl",
    version                 := sys.env.getOrElse("PRICE_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    Debian / debianPackageDependencies ++= wkhtmlDependencies,
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras,
      lagomOpenapiImpl,
      //      "org.camunda.bpm.dmn" % "camunda-engine-dmn-root" % "7.16.0",
      "org.camunda.bpm.dmn" % "camunda-engine-dmn" % "7.18.0" exclude ("org.camunda.feel", "feel-engine"),
      //      "org.camunda.feel" % "feel-scala-root" % "1.11.0",
      //      "org.camunda.feel" % "feel-scala" % "1.11.1",
      "org.camunda.feel" % "feel-engine" % "1.15.3"
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `price-api`,
    `supplier-api`,
    `security-api`,
    `customer-api`,
    `assembly-api`,
    utilsDep,
    `templating`,
    `pcb-api`,
    `pcb-model`,
    `impl-utils`,
    `assembly-commons`,
    `panel-api`,
    `layerstack-api`,
    `mailgun`,
    `camunda-api`
  )

lazy val `native-converter-pylib` = (project in file("framework/converter/pylib"))
  .settings(
    name    := "native-converter-pylib",
    version := sys.env.getOrElse("NATIVE_CONVERTER_PYLIB_VERSION", stackratePlatform)
  )

lazy val `native-converter-api` = (project in file("framework/converter/api"))
  .settings(
    name    := "native-converter-api",
    version := sys.env.getOrElse("NATIVE_CONVERTER_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `utils`, `assembly-api`, `pcb-model`)

lazy val `native-converter-impl` = (project in file("framework/converter/impl"))
  .enablePlugins(
    servicePlugins ++ Seq(): _*
  )
  .settings(
    name                    := "native-converter-impl",
    version                 := sys.env.getOrElse("NATIVE_CONVERTER_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `native-converter-api`,
    `native-converter-pylib`,
    utilsDep,
    `impl-utils`,
    `security-api`,
    `assembly-api`,
    `pcb-api`,
    `pcb-model`
  )

lazy val `panel-api` = (project in file("ems/panel/api"))
  .settings(
    name    := "ems-panel-api",
    version := sys.env.getOrElse("PANEL_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `assembly-commons`, `pcb-model`, `luminovo-model`)
//
//
//
lazy val `panel-impl` = (project in file("ems/panel/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "ems-panel-impl",
    version                 := sys.env.getOrElse("PANEL_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ batik ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(`panel-api`, utilsDep, `security-api`, `pcb-api`, `impl-utils`, `renderer-impl`)

lazy val `inbox-api` = (project in file("framework/inbox/api"))
  .settings(
    name    := "inbox-api",
    version := sys.env.getOrElse("INBOX_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `inbox-commons`, `impl-utils`)

lazy val `inbox-commons` = (project in file("framework/inbox/commons"))
  .settings(
    name    := "inbox-commons",
    version := sys.env.getOrElse("INBOX_COMMONS_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `utils`)
//
//
//
lazy val `inbox-impl` = (project in file("framework/inbox/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "inbox-impl",
    version                 := sys.env.getOrElse("INBOX_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    Debian / debianPackageDependencies ++= wkhtmlDependencies,
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras,
      "javax.mail"   % "javax.mail-api" % "1.6.2",
      "com.sun.mail" % "javax.mail"     % "1.6.2"
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(`inbox-api`, utilsDep, `templating`, `impl-utils`, `security-api`)

lazy val `analysis-api` = (project in file("ems/analysis/api"))
  .settings(
    name    := "analysis-api",
    version := sys.env.getOrElse("ANALYSIS_API_VERSION", stackratePlatform),
    libraryDependencies ++= Seq(
      lagomScaladslApi,
      lagomScaladslServer
    )
  ).dependsOn(utils, `utils`, `assembly-api`, `pcb-model`)
//
//
//

lazy val `analysis-impl` = (project in file("ems/analysis/impl"))
  .enablePlugins(servicePlugins: _*)
  .settings(
    name                    := "analysis-impl",
    version                 := sys.env.getOrElse("ANALYSIS_IMPL_VERSION", stackratePlatform),
    jibRegistry             := jib_registry,
    jibBaseImage            := jib_base_image,
    jibCustomRepositoryPath := Some(repository_shared_path + name.value.replace("-impl", "")),
    libraryDependencies ++= Seq(
      // servicelocator,
      filters,
      scalaTest,
      cassandraExtras
    ) ++ commonServiceDependencies,
    commonServiceSettings
  )
  .settings(lagomForkedTestSettings: _*)
  .dependsOn(
    `analysis-api`,
    utilsDep,
    `impl-utils`,
    `security-api`,
    `assembly-api`,
    `pcb-api`,
    `pcb-model`,
    `layerstack-api`,
    `dfm-api`
  )

lazy val dependencySources = taskKey[Unit]("List Dependency Folders")
lazy val directProjectDeps = taskKey[Map[String, Seq[ProjectRef]]]("List Dependency Folders")

directProjectDeps := {
  val extracted = Project extract state.value
  extracted.structure.allProjects.map { p =>
    val deps = p.dependencies.map(_.project)
    (p.id, deps)
  }.toMap
}

dependencySources := {
  val deps = directProjectDeps.value

  def transitives(id: String): Seq[ProjectRef] = {
    val direct = deps(id)
    direct ++ direct.flatMap(d => transitives(d.project))
  }

  val projects = (Project extract state.value).structure.allProjects.map(x => x.id -> x).toMap

  projects.values.foreach { rp =>
    val bases = (transitives(rp.id).map { p =>
      projects(p.project).base.absolutePath
    } :+ rp.base.absolutePath).distinct

    val file = (Compile / target).value / "paths" / s"${rp.id}.txt"
    IO.write(file, bases.mkString("\n") + "\n")
  }

}
