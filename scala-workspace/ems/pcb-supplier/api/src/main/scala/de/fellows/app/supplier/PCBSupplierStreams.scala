package de.fellows.app.supplier

import de.fellows.app.assembly.commons.AssemblyReference
import play.api.libs.json.{ Format, Json }

import java.time.Instant
import java.util.UUID

//object PCBSupplierStreams {

sealed trait SupplierMessage {}

case class PCBSupplierStreamMessage(t: String, m: SupplierMessage)

case class MatchingFinishedMessage(assembly: AssemblyReference, specification: UUID, info: Seq[SupplierManufacture])
    extends SupplierMessage

case class Ping(time: Instant) extends SupplierMessage

object SupplierMessage {
  implicit val format: Format[SupplierMessage] = Json.format
}

object PCBSupplierStreamMessage {
  implicit val format: Format[PCBSupplierStreamMessage] = Json.format[PCBSupplierStreamMessage]
}

object Ping {
  implicit val format: Format[Ping] = Json.format[Ping]
}

object MatchingFinishedMessage {
  implicit val format: Format[MatchingFinishedMessage] = Json.format[MatchingFinishedMessage]
}

//}
