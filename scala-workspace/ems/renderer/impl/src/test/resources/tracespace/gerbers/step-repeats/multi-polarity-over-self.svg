<svg
  version="1.1"
  xmlns="http://www.w3.org/2000/svg"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  stroke-width="0"
  width="1.5in" height="1.5in" viewBox="0 0 1500 1500">

  <defs>
    <mask id="multi-polarity-over-self-mask1" fill="#000">
      <circle cx="500" cy="1000" r="500" fill="#fff"/>
      <circle cx="500" cy="1000" r="400"/>
      <circle cx="500" cy="1000" r="300" fill="#fff"/>
      <circle cx="500" cy="1000" r="200"/>
      <circle cx="500" cy="1000" r="100" fill="#fff"/>
      <circle cx="500" cy="500" r="500"/>
      <circle cx="1000" cy="1000" r="500"/>
    </mask>
    <mask id="multi-polarity-over-self-mask2" fill="#000">
      <circle cx="500" cy="500" r="500" fill="#fff"/>
      <circle cx="500" cy="500" r="400"/>
      <circle cx="500" cy="500" r="300" fill="#fff"/>
      <circle cx="500" cy="500" r="200"/>
      <circle cx="500" cy="500" r="100" fill="#fff"/>
      <circle cx="1000" cy="500" r="500"/>
      <circle cx="1000" cy="1000" r="500"/>
    </mask>
    <mask id="multi-polarity-over-self-mask3" fill="#000">
      <circle cx="1000" cy="1000" r="500" fill="#fff"/>
      <circle cx="1000" cy="1000" r="400"/>
      <circle cx="1000" cy="1000" r="300" fill="#fff"/>
      <circle cx="1000" cy="1000" r="200"/>
      <circle cx="1000" cy="1000" r="100" fill="#fff"/>
      <circle cx="1000" cy="500" r="500"/>
    </mask>
    <mask id="multi-polarity-over-self-mask4" fill="#000">
      <circle cx="1000" cy="500" r="500" fill="#fff"/>
      <circle cx="1000" cy="500" r="400"/>
      <circle cx="1000" cy="500" r="300" fill="#fff"/>
      <circle cx="1000" cy="500" r="200"/>
      <circle cx="1000" cy="500" r="100" fill="#fff"/>
    </mask>
  </defs>

  <g fill="#39C">
    <circle mask="url(#multi-polarity-over-self-mask1)" cx="500" cy="1000" r="500"/>
    <circle mask="url(#multi-polarity-over-self-mask2)" cx="500" cy="500" r="500"/>
    <circle mask="url(#multi-polarity-over-self-mask3)" cx="1000" cy="1000" r="500"/>
    <circle mask="url(#multi-polarity-over-self-mask4)" cx="1000" cy="500" r="500"/>
  </g>
</svg>
