{"id": "38045474-2f2d-4ffe-b26a-e5ddb8ffcf69", "team": "demo", "name": "<PERSON><PERSON>", "stacks": [{"name": "Main-stack", "layers": [{"id": "45f6306b-e865-4f03-bc0c-d1ccd3283fb8", "layerType": "soldermask", "meta": {"CuCount": 0}, "materialRef": "JzWQ/o5jRzSyV9QE5DMcPg", "material": {"team": "demo", "id": "JzWQ/o5jRzSyV9QE5DMcPg", "name": "Polar Samples SM/001", "meta": {"losstangent": 0.0195, "size": "*", "supplier": "Polar Samples", "colour": "Green", "type": "SolderMask", "supplierdescription": "SM/001", "maskthickness": 25, "description": "Liquid PhotoImageable Mask", "tolerance": 10, "dielectricconstant": 4, "leadtime": 0}, "materialType": "soldermask"}}, {"id": "fe908791-992f-4017-ab00-c54209c102c2", "layerType": "foil", "meta": {"CuCount": 1}, "materialRef": "90nab4/ERcq3haGQdWDX2Q", "material": {"team": "demo", "id": "90nab4/ERcq3haGQdWDX2Q", "name": "Circuit Foil Luxembourg ED Copper Foil", "meta": {"description": "BF-ANP", "supplier": "Circuit Foil Luxembourg", "useinautostack": false, "cuthickness": 18, "leadtime": 7, "type": "Copper", "supplierdescription": "ED Copper Foil"}, "materialType": "foil"}}, {"id": "56db7df6-1a0f-4b36-92b5-ae99f6eaee34", "layerType": "prepreg", "meta": {"CuCount": 0}, "materialRef": "AxfnGdyOSWGpK35iSW6tmw", "material": {"team": "demo", "id": "AxfnGdyOSWGpK35iSW6tmw", "name": "Doosan DS-7408-<PERSON> 106", "meta": {"cafresistance": 0, "outer": true, "losstangent": 0.016, "size": "*", "zaxisexpansion": 0, "tg": 140, "supplier": "<PERSON><PERSON>", "excessresin": 8.5, "basethickness": 50, "type": "Dielectric", "laserdrillable": false, "description": 106, "tolerance": 10, "resincontent": 70, "useinautostack": false, "td": 0, "dielectricconstant": 4.08, "leadtime": 0, "supplierdescription": "DS-7408-BS 106", "finishedthickness": 50}, "materialType": "prepreg"}}, {"id": "f7418380-c2f3-4aaf-98eb-5b79f04f8cde", "layerType": "foil", "meta": {"CuCount": 1}, "materialRef": "90nab4/ERcq3haGQdWDX2Q", "material": {"team": "demo", "id": "90nab4/ERcq3haGQdWDX2Q", "name": "Circuit Foil Luxembourg ED Copper Foil", "meta": {"description": "BF-ANP", "supplier": "Circuit Foil Luxembourg", "useinautostack": false, "cuthickness": 18, "leadtime": 7, "type": "Copper", "supplierdescription": "ED Copper Foil"}, "materialType": "foil"}}, {"id": "f1f8ec18-95ee-4cda-b452-bf6a19ac6161", "layerType": "prepreg", "meta": {"CuCount": 0}, "materialRef": "AxfnGdyOSWGpK35iSW6tmw", "material": {"team": "demo", "id": "AxfnGdyOSWGpK35iSW6tmw", "name": "Doosan DS-7408-<PERSON> 106", "meta": {"cafresistance": 0, "outer": true, "losstangent": 0.016, "size": "*", "zaxisexpansion": 0, "tg": 140, "supplier": "<PERSON><PERSON>", "excessresin": 8.5, "basethickness": 50, "type": "Dielectric", "laserdrillable": false, "description": 106, "tolerance": 10, "resincontent": 70, "useinautostack": false, "td": 0, "dielectricconstant": 4.08, "leadtime": 0, "supplierdescription": "DS-7408-BS 106", "finishedthickness": 50}, "materialType": "prepreg"}}, {"id": "2c55d96d-4b2f-4e40-aeb3-0e76879c3887", "layerType": "core", "meta": {"CuCount": 2}, "materialRef": "7VNQElwTTyGjjlmcJVos9w", "material": {"team": "demo", "id": "7VNQElwTTyGjjlmcJVos9w", "meta": {"losstangent": "", "supplier": "", "resincontent": "", "dielectricconstant": "", "basethickness": 50, "type": "", "supplierdescription": "", "lowercuthickness": 10, "description": "", "tolerance": "", "uppercuthickness": 10}, "materialType": "core"}}, {"id": "7142830b-4efc-4a11-9f98-74c5b40792e8", "layerType": "prepreg", "meta": {"CuCount": 0}, "materialRef": "AxfnGdyOSWGpK35iSW6tmw", "material": {"team": "demo", "id": "AxfnGdyOSWGpK35iSW6tmw", "name": "Doosan DS-7408-<PERSON> 106", "meta": {"cafresistance": 0, "outer": true, "losstangent": 0.016, "size": "*", "zaxisexpansion": 0, "tg": 140, "supplier": "<PERSON><PERSON>", "excessresin": 8.5, "basethickness": 50, "type": "Dielectric", "laserdrillable": false, "description": 106, "tolerance": 10, "resincontent": 70, "useinautostack": false, "td": 0, "dielectricconstant": 4.08, "leadtime": 0, "supplierdescription": "DS-7408-BS 106", "finishedthickness": 50}, "materialType": "prepreg"}}, {"id": "63cca912-2258-4709-b9c9-a9af487a73a9", "layerType": "foil", "meta": {"CuCount": 1}, "materialRef": "90nab4/ERcq3haGQdWDX2Q", "material": {"team": "demo", "id": "90nab4/ERcq3haGQdWDX2Q", "name": "Circuit Foil Luxembourg ED Copper Foil", "meta": {"description": "BF-ANP", "supplier": "Circuit Foil Luxembourg", "useinautostack": false, "cuthickness": 18, "leadtime": 7, "type": "Copper", "supplierdescription": "ED Copper Foil"}, "materialType": "foil"}}, {"id": "9cfe300b-4ebe-4e75-8f57-44d8f2f16e7b", "layerType": "prepreg", "meta": {"CuCount": 0}, "materialRef": "AxfnGdyOSWGpK35iSW6tmw", "material": {"team": "demo", "id": "AxfnGdyOSWGpK35iSW6tmw", "name": "Doosan DS-7408-<PERSON> 106", "meta": {"cafresistance": 0, "outer": true, "losstangent": 0.016, "size": "*", "zaxisexpansion": 0, "tg": 140, "supplier": "<PERSON><PERSON>", "excessresin": 8.5, "basethickness": 50, "type": "Dielectric", "laserdrillable": false, "description": 106, "tolerance": 10, "resincontent": 70, "useinautostack": false, "td": 0, "dielectricconstant": 4.08, "leadtime": 0, "supplierdescription": "DS-7408-BS 106", "finishedthickness": 50}, "materialType": "prepreg"}}, {"id": "1b73fd14-7c05-4b63-b2bd-f68f88d07fff", "layerType": "foil", "meta": {"CuCount": 1}, "materialRef": "90nab4/ERcq3haGQdWDX2Q", "material": {"team": "demo", "id": "90nab4/ERcq3haGQdWDX2Q", "name": "Circuit Foil Luxembourg ED Copper Foil", "meta": {"description": "BF-ANP", "supplier": "Circuit Foil Luxembourg", "useinautostack": false, "cuthickness": 18, "leadtime": 7, "type": "Copper", "supplierdescription": "ED Copper Foil"}, "materialType": "foil"}}, {"id": "55a87124-f68c-4fce-a219-3d45507c68dc", "layerType": "soldermask", "meta": {"CuCount": 0}, "materialRef": "JzWQ/o5jRzSyV9QE5DMcPg", "material": {"team": "demo", "id": "JzWQ/o5jRzSyV9QE5DMcPg", "name": "Polar Samples SM/001", "meta": {"losstangent": 0.0195, "size": "*", "supplier": "Polar Samples", "colour": "Green", "type": "SolderMask", "supplierdescription": "SM/001", "maskthickness": 25, "description": "Liquid PhotoImageable Mask", "tolerance": 10, "dielectricconstant": 4, "leadtime": 0}, "materialType": "soldermask"}}], "stackType": "rigid", "drills": [{"from": 0, "to": 5, "col": 0, "drillType": "PTH"}, {"from": 3, "to": 4, "col": 1, "drillType": "Buried"}, {"from": 2, "to": 3, "col": 4, "drillType": "Laser"}, {"from": 5, "to": 4, "col": 5, "drillType": "Laser"}], "offset": 0}], "metaInfo": {"CuCount": 6, "TotalHeight": 392}}