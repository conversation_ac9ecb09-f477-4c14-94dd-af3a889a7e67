package de.fellows.ems.dfm.impl

import akka.Done
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.Analysis
import de.fellows.app.assemby.api.{AssemblyService, SuccessAssemblyLifecycle}
import de.fellows.utils.TopicUtils

import scala.concurrent.ExecutionContext

class AssemblyListener(ass: AssemblyService)(implicit c: ExecutionContext) {
  val started = System.currentTimeMillis()

  var app: DFMServiceApp = _

  def withApp(app: DFMServiceApp): AssemblyListener = {
    this.app = app
    this
  }

  TopicUtils.subscribeLatest(ass.lifecycleTopic(), started, 1) { m =>
    m.payload.newLifecycle match {
      case SuccessAssemblyLifecycle(Analysis) =>
        this.app.srv._doAnalyzeBoard(
          m.payload.assemblyReference.team,
          m.payload.assemblyReference.id,
          m.payload.assemblyReference.version
        )
          .map(_ => Done)
    }
  }

}
