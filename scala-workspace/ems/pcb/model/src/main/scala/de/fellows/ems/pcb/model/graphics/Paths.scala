package de.fellows.ems.pcb.model.graphics

import de.fellows.ems.pcb.model.GraphicUtils
import de.fellows.ems.pcb.model.graphics.ops.DistanceOp
import de.fellows.ems.pcb.model.graphics.tree.Distance
import de.fellows.ems.pcb.model.graphics.tree.IntersectsException
import de.fellows.ems.pcb.model.graphics.tree.TraceWidthDescription

import java.awt.Shape
import java.awt.geom.Area
import java.awt.geom.CubicCurve2D
import java.awt.geom.GeneralPath
import java.awt.geom.Line2D
import java.awt.geom.PathIterator
import java.awt.geom.PathIterator.SEG_CLOSE
import java.awt.geom.PathIterator.SEG_CUBICTO
import java.awt.geom.PathIterator.SEG_LINETO
import java.awt.geom.PathIterator.SEG_MOVETO
import java.awt.geom.PathIterator.SEG_QUADTO
import java.awt.geom.QuadCurve2D
import java.awt.geom.Rectangle2D
import scala.math.BigDecimal.RoundingMode

/** Represents a Path-Segment in a [[PathIterator]], but containing the necessary context as well.
  * I.e. a single [[PathSegment]] can be used without knowing all the other paths
  *
  * @tparam T
  */
sealed trait PathSegment {
  val segment: Int
  val windingRule: Int

}

/** A subset of [[PathSegment segments]] that represent only the visible elements that can intersect with other
  * segments.
  * i.e. everything except [[MoveTo moves]]
  */
sealed trait IntersectablePathSegment extends PathSegment with Intersectable[IntersectablePathSegment] {
  def getShape: Shape

  override def id: Option[String] = None

  override def fastIntersects(other: IntersectablePathSegment): Boolean =
    this.bounds.intersects(other.bounds)

  override def intersects(other: Rectangle2D): Boolean = this.getShape.intersects(other)

  override def bounds: Rectangle2D =
    Graphic.extendRect(this.getShape.getBounds2D, 1)

  override def isNeighbor(other: IntersectablePathSegment, margin: Double): Boolean =
    GraphicUtils.isBoundsNeighbor(margin, other.getShape.getBounds2D, this.getShape.getBounds2D)

  override def contains(other: GPoint): Boolean = getShape.contains(other.x, other.y)

  override def boundingDistance(other: IntersectablePathSegment, max: Option[Double]): Distance =
    GraphicUtils.distanceBetweenRectangles(this.bounds, other.bounds)

  override def intersects(other: IntersectablePathSegment): Boolean =
    Graphic.intersects(new Area(this.getShape), new Area(other.getShape))

  def length: Double

  def toPoints: Seq[GPoint]
}

case class MoveTo(x: Double, y: Double, override val windingRule: Int) extends PathSegment {
  override val segment = SEG_MOVETO
}

case class LineTo(fromX: Double, fromY: Double, toX: Double, toY: Double, override val windingRule: Int)
    extends IntersectablePathSegment {
  override val segment = SEG_LINETO

  lazy val line: Line2D.Double = new Line2D.Double(fromX, fromY, toX, toY)

  override def getShape: Shape = line

  override def intersects(other: IntersectablePathSegment): Boolean = other match {
    case x: LineTo => Paths.intersects(this, x)
    case x: Close  => Paths.intersects(this, x)
    case x         => super.intersects(x)
  }

  override def distance(other: IntersectablePathSegment, max: Option[Double], scaling: Double): Seq[Distance] =
    other match {
      case LineTo(otherFromX, otherFromY, otherToX, otherToY, windingRule) =>
        Geometry.lineToLine(
          GPoint(fromX, fromY),
          GPoint(toX, toY),
          GPoint(otherFromX, otherFromY),
          GPoint(otherToX, otherToY)
        )
      case QuadTo(fromX, fromY, cp1X, cp1Y, cp2X, cp2Y, windingRule) => ???
      case other: CubicTo =>
        DistanceOp.cubicToLine(
          Seq(
            GPoint(other.fromX, other.fromY),
            GPoint(other.cp1X, other.cp1Y),
            GPoint(other.cp2X, other.cp2Y),
            GPoint(other.cp3X, other.cp3Y)
          ),
          Seq(
            GPoint(fromX, fromY),
            GPoint(toX, toY)
          )
        )
      case other: Close =>
        Geometry.lineToLine(
          GPoint(fromX, fromY),
          GPoint(toX, toY),
          GPoint(other.fromX, other.fromY),
          GPoint(other.toX, other.toY)
        )
    }

  override def thinness(margin: Option[Double], scaling: Double): Seq[TraceWidthDescription] = Seq()

  override def length: Double = Geometry.distance(fromX, fromY, toX, toY)

  def toPoints: Seq[GPoint] =
    Seq(
      GPoint(fromX, fromY),
      GPoint(toX, toY)
    )

  def P(t: Double): GPoint = {
    val x = this.fromX + t * (this.toX - this.fromX)
    val y = this.fromY + t * (this.toY - this.fromY)
    GPoint(x, y)
  }

}

case class QuadTo(
    fromX: Double,
    fromY: Double,
    cp1X: Double,
    cp1Y: Double,
    cp2X: Double,
    cp2Y: Double,
    override val windingRule: Int
) extends IntersectablePathSegment {
  override val segment = SEG_QUADTO

  lazy val curve = new QuadCurve2D.Double(fromX, fromY, cp1X, cp1Y, cp2X, cp2Y)

  override def getShape: Shape = curve

  override def distance(other: IntersectablePathSegment, max: Option[Double], scaling: Double): Seq[Distance] = ???

  override def thinness(margin: Option[Double], scaling: Double): Seq[TraceWidthDescription] = Seq()

  override def intersects(other: IntersectablePathSegment): Boolean =
    Graphic.intersects(new Area(this.getShape), new Area(other.getShape))

  override def length: Double = Geometry.distance(fromX, fromY, cp2X, cp2Y)

  lazy val _points = Seq(
    GPoint(fromX, fromY),
    GPoint(cp1X, cp1Y),
    GPoint(cp2X, cp2Y)
  )

  def toPoints: Seq[GPoint] = _points

  /** interpolate a point on this curve. Defined in [[SEG_QUADTO]]
    *
    * @param t
    * 0 <= t <= 1
    * @return
    */
  def P(t: Double): GPoint = {
    // Some parts of this formula are constant, so we can tweak it a bit to avoid some
    // calculations at run time (seems to improve performance a little bit)

    // def B(C: Int, n: Int, m: Int): Double = C * Math.pow(t, m) * Math.pow(1 - t, n - m)
    // def C(n: Int, m: Int) = factorial(n) / (factorial(m) * factorial(n - m))

    // C(2,0) = 1, C(2,1) = 2, C(2,2) = 1
    // Likewise, in B, we can remove one of the powers of t or (1-t) because for some cases they are either 0 or 1

    val b0 = Math.pow(1 - t, 2)
    val b1 = 2 * t * (1 - t)
    val b2 = Math.pow(t, 2)

    GPoint(
      x = fromX * b0 + cp1X * b1 + cp2X * b2,
      y = fromY * b0 + cp1Y * b1 + cp2Y * b2
    )
  }

  def boundingBox(numberOfPoints: Int): (GPoint, GPoint) = {
    var minX: Double = Double.PositiveInfinity
    var minY: Double = Double.PositiveInfinity
    var maxX: Double = Double.NegativeInfinity
    var maxY: Double = Double.NegativeInfinity

//    val nP        = Math.min(Math.floor(length * 5).toInt, numberOfPoints)
    val increment = 1.0 / numberOfPoints
    var t         = 0.0

    while (t < 1.0) {
      val b0 = Math.pow(1 - t, 2)
      val b1 = 2 * t * (1 - t)
      val b2 = Math.pow(t, 2)

      val x = fromX * b0 + cp1X * b1 + cp2X * b2
      val y = fromY * b0 + cp1Y * b1 + cp2Y * b2

      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x)
      maxY = Math.max(maxY, y)

      t += increment
    }

    (GPoint(minX, minY), GPoint(maxX, maxY))
  }

}

case class CubicTo(
    fromX: Double,
    fromY: Double,
    cp1X: Double,
    cp1Y: Double,
    cp2X: Double,
    cp2Y: Double,
    cp3X: Double,
    cp3Y: Double,
    override val windingRule: Int
) extends IntersectablePathSegment {
  override val segment = SEG_CUBICTO

  lazy val _points = Seq(
    GPoint(fromX, fromY),
    GPoint(cp1X, cp1Y),
    GPoint(cp2X, cp2Y),
    GPoint(cp3X, cp3Y)
  )

  def toPoints: Seq[GPoint] = _points

  override def getShape: Shape = new CubicCurve2D.Double(fromX, fromY, cp1X, cp1Y, cp2X, cp2Y, cp3X, cp3Y)

  override def distance(other: IntersectablePathSegment, max: Option[Double], scaling: Double): Seq[Distance] =
    other match {
      case x: LineTo => x.distance(this, max, scaling)
      case x: QuadTo => x.distance(this, max, scaling)
      case x: CubicTo =>
        DistanceOp.cubicToCubic(
          this.toPoints,
          x.toPoints
        )
      case x: Close => x.asLine.distance(this, max, scaling)
    }

  override def thinness(margin: Option[Double], scaling: Double): Seq[TraceWidthDescription] = ???

  override def length: Double = Geometry.distance(fromX, fromY, cp3X, cp3Y)

  /** interpolate a point on this curve. Defined in [[SEG_CUBICTO]]
    *
    * @param t
    * 0 <= t <= 1
    * @return
    */
  def P(t: Double): GPoint = {
    // Some parts of this formula are constant, so we can tweak it a bit to avoid some
    // calculations at run time (seems to improve performance a little bit)

    // def B(C: Int, n: Int, m: Int): Double = C * Math.pow(t, m) * Math.pow((1 - t), (n - m))
    // def C(n: Int, m: Int) = factorial(n) / (factorial(m) * factorial(n - m))

    // C(3,0) = 1, C(3,1) = 3, C(3,2) = 3, C(3,3) = 1
    // Likewise, in B, we can remove one of the powers of t or (1-t) because for some cases they are either 0 or 1

    val b0 = Math.pow(1 - t, 3)
    val b1 = 3 * t * Math.pow(1 - t, 2)
    val b2 = 3 * Math.pow(t, 2) * (1 - t)
    val b3 = Math.pow(t, 3)

    GPoint(
      x = fromX * b0 + cp1X * b1 + cp2X * b2 + cp3X * b3,
      y = fromY * b0 + cp1Y * b1 + cp2Y * b2 + cp3Y * b3
    )
  }

  def boundingBox(numberOfPoints: Int): (GPoint, GPoint) = {
    var minX = Double.PositiveInfinity
    var minY = Double.PositiveInfinity
    var maxX = Double.NegativeInfinity
    var maxY = Double.NegativeInfinity

//    val nP        = Math.min(Math.floor(length * 5).toInt, numberOfPoints)
    val increment = 1.0 / numberOfPoints
    var t         = 0.0

    while (t < 1.0) {
      val b0 = Math.pow(1 - t, 3)
      val b1 = 3 * t * Math.pow(1 - t, 2)
      val b2 = 3 * Math.pow(t, 2) * (1 - t)
      val b3 = Math.pow(t, 3)

      val x = fromX * b0 + cp1X * b1 + cp2X * b2 + cp3X * b3
      val y = fromY * b0 + cp1Y * b1 + cp2Y * b2 + cp3Y * b3

      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x)
      maxY = Math.max(maxY, y)

      t += increment
    }

    (GPoint(minX, minY), GPoint(maxX, maxY))
  }

}

case class Close(fromX: Double, fromY: Double, toX: Double, toY: Double, override val windingRule: Int)
    extends IntersectablePathSegment {

  override def toPoints: Seq[GPoint] = asLine.toPoints

  lazy val asLine = LineTo(fromX, fromY, toX, toY, windingRule)

  override val segment = SEG_CLOSE

  override def getShape: Shape = asLine.getShape

  override def distance(other: IntersectablePathSegment, max: Option[Double], scaling: Double): Seq[Distance] =
    asLine.distance(other, max, scaling)

  override def thinness(margin: Option[Double], scaling: Double): Seq[TraceWidthDescription] =
    asLine.thinness(margin, scaling)

  override def intersects(other: IntersectablePathSegment): Boolean = asLine.intersects(other)

  override def length: Double = Geometry.distance(fromX, fromY, toX, toY)
}

object Paths {
  implicit class FunctionalPathIterator(pi: PathIterator) {

    def foreach[T](cb: PathSegment => T): Unit =
      loop { segment =>
        cb(segment)
        true
      }

    def map[T](cb: PathSegment => T): Seq[T] = {
      val b = Seq.newBuilder[T]
      foreach(x => b += cb(x))
      b.result()
    }

    def flatMap[T](cb: PathSegment => IterableOnce[T]): Seq[T] = {
      val b = Seq.newBuilder[T]
      foreach(x => b.addAll(cb(x)))
      b.result()
    }

    def toSeq(): Seq[PathSegment] =
      map(x => x)

    def find(fn: PathSegment => Boolean): Option[PathSegment] = {
      var result: Option[PathSegment] = None
      loop { segment =>
        if (fn(segment)) {
          result = Some(segment)
        }
        result.isEmpty
      }
      result
    }

    /** Iterate over the path, calling fn for each segment.
      * If fn returns false, the iteration stops.
      */
    private def loop(fn: PathSegment => Boolean): Unit = {
      val check    = new Array[Double](6)
      var continue = true

      var currentX  = 0.0
      var currentY  = 0.0
      var lastMoveX = 0.0
      var lastMoveY = 0.0

      while (continue && !pi.isDone) {
        val cs = pi.currentSegment(check)
        val wr = pi.getWindingRule

        val triedCurve = cs match {
          case SEG_MOVETO =>
            currentX = check(0)
            currentY = check(1)
            lastMoveX = check(0)
            lastMoveY = check(1)

            MoveTo(currentX, currentY, wr)

          case SEG_LINETO =>
            val fromX = currentX
            val fromY = currentY
            currentX = check(0)
            currentY = check(1)

            LineTo(fromX, fromY, currentX, currentY, wr)

          case SEG_QUADTO =>
            val fromX = currentX
            val fromY = currentY
            currentX = check(2)
            currentY = check(3)

            QuadTo(fromX, fromY, check(0), check(1), currentX, currentY, wr)

          case SEG_CUBICTO =>
            val fromX = currentX
            val fromY = currentY
            currentX = check(4)
            currentY = check(5)

            CubicTo(fromX, fromY, check(0), check(1), check(2), check(3), currentX, currentY, wr)

          case SEG_CLOSE =>
            val fromX = currentX
            val fromY = currentY
            currentX = lastMoveX
            currentY = lastMoveY

            val lmX = lastMoveX
            val lmY = lastMoveY

            lastMoveX = 0.0
            lastMoveY = 0.0

            Close(fromX, fromY, lmX, lmY, wr)
        }

        continue = fn(triedCurve)

        pi.next()
      }
    }

  }

  private def toLine(curve: PathSegment): Option[Line2D.Double] = {
    val scale = 2
    val line = curve match {
      case crv: LineTo =>
        Some(new Line2D.Double(
          BigDecimal(crv.fromX).setScale(scale, RoundingMode.DOWN).doubleValue,
          BigDecimal(crv.fromY).setScale(scale, RoundingMode.DOWN).doubleValue,
          BigDecimal(crv.toX).setScale(scale, RoundingMode.DOWN).doubleValue,
          BigDecimal(crv.toY).setScale(scale, RoundingMode.DOWN).doubleValue
        ))
      case crv: Close =>
        Some(new Line2D.Double(
          BigDecimal(crv.fromX).setScale(scale, RoundingMode.DOWN).doubleValue,
          BigDecimal(crv.fromY).setScale(scale, RoundingMode.DOWN).doubleValue,
          BigDecimal(crv.toX).setScale(scale, RoundingMode.DOWN).doubleValue,
          BigDecimal(crv.toY).setScale(scale, RoundingMode.DOWN).doubleValue
        ))
      case _ => None
    }

    line.filter { l =>
      val xval   = l.x1 - l.x2
      val yval   = l.y1 - l.y2
      val length = Math.sqrt(xval * xval + yval * yval)

      length > 0
    }
  }

  // round to 2 decimal places
  def round(d: Double): Double =
    BigDecimal(d).setScale(2, RoundingMode.DOWN).doubleValue

  def intersects(c1: PathSegment, c2: PathSegment): Boolean =
    (c1, c2) match {
      case (IntersectableLine(fromX1, fromY1, toX1, toY1), IntersectableLine(fromX2, fromY2, toX2, toY2)) =>
        val r = Line2D.linesIntersect(
          fromX1,
          fromY1,
          toX1,
          toY1,
          fromX2,
          fromY2,
          toX2,
          toY2
        )

        r
      case _ => false
    }

  def countourIntersects(a: Shape, b: Shape): Boolean =
    try {
      a.getPathIterator(null).foreach { c1 =>
        b.getPathIterator(null).foreach { c2 =>
          if (Paths.intersects(c1, c2)) {
            throw IntersectsException()
          }
        }
      }

      false
    } catch {
      case _: IntersectsException => true
    }

}

object IntersectableLine {

  private def mbRoundedIntersectableLine(
      fromX: Double,
      fromY: Double,
      toX: Double,
      toY: Double
  ): Option[(Double, Double, Double, Double)] = {
    val roundedFromX = Paths.round(fromX)
    val roundedFromY = Paths.round(fromY)
    val roundedToX   = Paths.round(toX)
    val roundedToY   = Paths.round(toY)

    Option.when(Geometry.distance(roundedFromX, roundedFromY, roundedToX, roundedToY) > 0.0) {
      (roundedFromX, roundedFromY, roundedToX, roundedToY)
    }
  }

  def unapply(pathSegment: PathSegment): Option[(Double, Double, Double, Double)] =
    pathSegment match {
      case l: LineTo => mbRoundedIntersectableLine(l.fromX, l.fromY, l.toX, l.toY)
      case c: Close  => mbRoundedIntersectableLine(c.fromX, c.fromY, c.toX, c.toY)
      case _         => None
    }
}
