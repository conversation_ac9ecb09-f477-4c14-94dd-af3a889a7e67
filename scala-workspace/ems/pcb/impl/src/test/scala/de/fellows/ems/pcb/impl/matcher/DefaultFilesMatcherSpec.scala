package de.fellows.ems.pcb.impl.matcher

import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.OptionValues
import org.yaml.snakeyaml.Yaml

import java.io.{File, FileInputStream}
import java.nio.file.{Path, Paths}
import scala.jdk.CollectionConverters.{getClass, _}

class DefaultFilesMatcherSpec extends AnyWordSpec with Matchers with OptionValues {

  implicit val serviceDefinition: ServiceDefinition = ServiceDefinition("test-service")

  case class ExpectedFileMatch(
      filename: String,
      expectedFileType: Option[String],
      expectedCategory: Option[String]
  )

  case class TestProject(
      name: String,
      path: Path,
      expectedMatches: Seq[ExpectedFileMatch]
  )

  private def loadExpectedResults(projectPath: Path): Seq[ExpectedFileMatch] = {
    val yamlFile = projectPath.resolve("expected.yaml").toFile
    if (!yamlFile.exists()) {
      fail(s"Expected YAML file not found: ${yamlFile.getAbsolutePath}")
    }

    val yaml  = new Yaml()
    val data  = yaml.load(new FileInputStream(yamlFile)).asInstanceOf[java.util.Map[String, Any]]
    val files = data.get("files").asInstanceOf[java.util.List[java.util.Map[String, Any]]]

    files.asScala.map { fileData =>
      ExpectedFileMatch(
        filename = fileData.get("filename").asInstanceOf[String],
        expectedFileType = Option(fileData.get("expectedFileType")).map(_.asInstanceOf[String]),
        expectedCategory = Option(fileData.get("expectedCategory")).map(_.asInstanceOf[String])
      )
    }.toSeq
  }

  private def discoverTestProjects(): Seq[TestProject] = {
    val gerberResourcesPath = Paths.get(getClass.getResource("/gerber-assets").toURI)

    gerberResourcesPath.toFile.listFiles()
      .filter(_.isDirectory)
      .filter(dir => new File(dir, "expected.yaml").exists())
      .map { projectDir =>
        val projectPath = projectDir.toPath
        TestProject(
          name = projectDir.getName,
          path = projectPath,
          expectedMatches = loadExpectedResults(projectPath)
        )
      }.toSeq
  }

  private def testProject(project: TestProject): Unit = {
    val files = project.path.toFile.listFiles()
      .filter(_.isFile)
      .filter(_.getName != "expected.yaml")
      .map(f => FilePath(f.getAbsolutePath))
      .toSeq

    val matcher = DefaultFilesMatcher(files)
    val results = matcher.matchFileType()

    // Verify all expected files are present
    val expectedFileNames = project.expectedMatches.map(_.filename).sorted
    val actualFileNames   = files.map(_.filename).sorted
    expectedFileNames.toSeq should be(actualFileNames.toSeq)

    // Collect all failures instead of failing on first mismatch
    val failures = project.expectedMatches.flatMap { expected =>
      val testFile = files.find(_.filename == expected.filename)
      if (testFile.isEmpty) {
        Some(s"❌ File not found: ${expected.filename}")
      } else {
        val actualResult   = results.get(testFile.get).flatten
        val actualFileType = actualResult.map(_.fileType)
        val actualCategory = actualResult.flatMap(_.category)

        if ((actualFileType, actualCategory) != (expected.expectedFileType, expected.expectedCategory)) {
          Some(s"❌ ${expected.filename}:\n" +
            s"   Expected: (${expected.expectedFileType}, ${expected.expectedCategory})\n" +
            s"   Actual:   ($actualFileType, $actualCategory)")
        } else {
          None
        }
      }
    }

    // Report all failures at once
    if (failures.nonEmpty) {
      val successCount = project.expectedMatches.length - failures.length
      val summary = s"\n🔍 Project ${project.name} - ${failures.length} failures, $successCount successes:\n\n" +
        failures.mkString("\n\n") +
        s"\n\n📊 Summary: $successCount/${project.expectedMatches.length} files matched correctly"
      fail(summary)
    } else {
      info(s"✅ All ${project.expectedMatches.length} files in ${project.name} matched correctly!")
    }
  }

  "DefaultFilesMatcher" should {

    "match files according to YAML expectations for all test projects" in {
      val projects = discoverTestProjects()

      if (projects.isEmpty) {
        fail("No test projects found with expected.yaml files. Please add test projects to /gerber-assets/ resources.")
      } else {
        info(s"Found ${projects.length} test projects: ${projects.map(_.name).mkString(", ")}")
        projects.foreach(testProject)
      }
    }
  }

  // Individual test methods for each discovered project will be generated dynamically
  discoverTestProjects().foreach { project =>
    s"DefaultFilesMatcher for project ${project.name}" should {
      s"correctly match all files in ${project.name}" in {
        testProject(project)
      }
    }
  }

  "debug single file" in {
    val projectName = "HTL71-0B_DAT-221130A"
    val fileName    = "PCB_L2.art"

    val gerberResourcesPath = Paths.get(getClass.getResource("/gerber-assets").toURI)
    val projectDir          = gerberResourcesPath.resolve(projectName).toFile

    if (!projectDir.exists()) {
      fail(s"Project directory not found: $projectName")
    }

    val targetFile = new File(projectDir, fileName)
    if (!targetFile.exists()) {
      fail(s"File not found: $fileName in project $projectName")
    }

    val filePath = FilePath(targetFile.getAbsolutePath)
    println(s"🔍 Debug matching for: ${filePath.filename}")
    println(s"📂 Full path: ${filePath.toPath}")

    // Test with single file
    val matcher = DefaultFilesMatcher(Seq(filePath))
    val results = matcher.matchFileType()

    val result = results.get(filePath).flatten

    println(s"🎯 Matching result:")
    result match {
      case Some(fileType) =>
        println(s"   ✅ Matched!")
        println(s"   📋 File Type: ${fileType.fileType}")
        println(s"   📂 Category: ${fileType.category.getOrElse("none")}")
        println(s"   🏷️  Service: ${fileType.service}")
        println(s"   🔢 Production File: ${fileType.productionFile}")
        println(s"   📄 MIME Type: ${fileType.mimeType.getOrElse("none")}")
    }
  }
}
