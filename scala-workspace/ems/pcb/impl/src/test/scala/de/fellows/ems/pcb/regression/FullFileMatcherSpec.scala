package de.fellows.ems.pcb.regression

import de.fellows.ems.pcb.impl.matcher.{DefaultFile<PERSON>atcher, DefaultFilesMatcher, FileMatch}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.{CSVUtils, FileType}
import org.scalatest.BeforeAndAfterAll
import org.scalatest.Inspectors.forAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.io.File
import java.nio.file.{Path, Paths}

class FullFileMatcherSpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {

  // TODO: this will eventually be the test run as the regression test in gitlab
  Utils.walk(Paths.get(
    "/home/<USER>/Code/fellows/board-test-lfs/filematcher/bmk-matched"
  )).filter(p =>
    p.toFile.getName == "files.csv"
  ).foreach { filescsv =>
    val assemblyFolder = filescsv.getParent.toFile

    s"${assemblyFolder.getName}" should {
      f"correctly match" in {
        val expected =
          CSVUtils.readCSV(assemblyFolder.toPath.resolve("files.csv"), separator = ";")
            .map(row => row("filename") -> row("filetype")).toMap
        val matches = createMatches(assemblyFolder)

        matches.foreach { xx =>
          val fileName     = xx._1.toFile.getName
          val expectedType = expected(fileName.replace(";", "_")).toLowerCase
          val actualType   = xx._2.getOrElse(FileType.UNKNOWN).fileType.toLowerCase

          println(s"$fileName: \t $actualType =? $expectedType")
        }

        forAll(matches.toSeq) { xx =>
          val fileName = xx._1.toFile.getName
          withClue(xx._1.toAbsolutePath) {
            val expectedType = expected(fileName.replace(";", "_")).toLowerCase
            val actualType   = xx._2.getOrElse(FileType.UNKNOWN).fileType.toLowerCase

            println(s"check ${actualType} == ${expectedType}  (${xx._2.flatMap(_.category)})")
            if (!xx._2.flatMap(_.category).contains(LayerConstants.Categories.mechanicalName)) {
              actualType should be(expectedType)
            }
          }
        }
      }
    }
  }

  private def createOldMatches(assemblyFolder: File): Map[Path, Option[FileType]] = {
    val matches: Map[Path, Seq[FileMatch]] = Utils.walk(assemblyFolder.toPath.resolve("files")).flatMap { p =>
      val fp = FilePath(p.toAbsolutePath.toString)
      val r =
        new DefaultFileMatcher(fp, p.toFile.getName, Seq(fp))(ServiceDefinition("unit test"))
          .matchFile()
          .map(p -> _._2)

      r
    }
      .groupBy(_._1).collect { case (k, v) => k -> v.flatMap(_._2) }

    DefaultFilesMatcher.chooseMatches[Path](matches, Seq(), x => FilePath(x.toAbsolutePath.toString))
      .map { x =>
        x._1 -> x._2.map(m => DefaultFilesMatcher.toType(m, ServiceDefinition("unit test")))
      }
  }

  private def createMatches(assemblyFolder: File): Map[Path, Option[FileType]] =
    new DefaultFilesMatcher[Path](
      Utils.walk(assemblyFolder.toPath.resolve("files")),
      x => FilePath(x.toAbsolutePath.toString),
      _ => None
    )(ServiceDefinition("unit test"))
      .matchFileType()
}
