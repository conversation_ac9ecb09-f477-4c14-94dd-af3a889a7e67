package de.fellows.ems.pcb.impl

import akka.Done
import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.{PersistentEntity, PersistentEntityRegistry}
import de.fellows.app.assembly.commons.AssemblyFiles.getLocalResourceFolder
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.Analysis
import de.fellows.app.assemby.api._
import de.fellows.ems.pcb.api.PCBV2Layer
import de.fellows.ems.pcb.impl.AssemblyListener.doCreatePCBForAssembly
import de.fellows.ems.pcb.impl.entity.pcb._
import de.fellows.ems.pcb.impl.entity.specification.SpecificationCommands.{CreateSpecification, GetSpecification}
import de.fellows.ems.pcb.impl.entity.specification.SpecificationEntity
import de.fellows.ems.pcb.impl.matcher.MatcherCoordinator
import de.fellows.ems.pcb.impl.svix.SvixEvents.PCBSpecificationChanged
import de.fellows.ems.pcb.model.{GerberFile, LayerConstants, PCBSpecification, PCBVersion, SpecificationStatus}
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.{FileType, StageStatusName}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.streams.ValidMessage
import de.fellows.utils.svix.SvixHelper
import de.fellows.utils.telemetry.KamonUtils
import de.fellows.utils.{streams, HashUtils, TopicUtils}
import kamon.annotation.api.Trace
import org.apache.commons.codec.digest.DigestUtils
import org.slf4j.LoggerFactory
import play.api.Logging

import java.io.FileInputStream
import java.util.UUID
import scala.concurrent.duration._
import scala.concurrent.{blocking, Await, ExecutionContext, Future}
import scala.reflect.ClassTag
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try, Using}
import de.fellows.luminovo.client.customparts.alerts.CustomPartsAlerts
import de.fellows.luminovo.client.customparts.alerts.model.NoOutlineFile
import de.fellows.luminovo.client.customparts.alerts.model.Resolved
import de.fellows.luminovo.client.customparts.alerts.model.Active
import de.fellows.luminovo.client.customparts.alerts.model.Pcb
import de.fellows.luminovo.client.customparts.alerts.model.NoDrillFile
import de.fellows.luminovo.client.customparts.alerts.model.CustomPartAlertStatus
import de.fellows.luminovo.client.customparts.alerts.model.NoSoldermask
import de.fellows.luminovo.client.customparts.alerts.model.NoSilkscreen

class AssemblyListener(
    assemblyService: AssemblyService,
    system: ActorSystem,
    eReg: PersistentEntityRegistry,
    alerts: CustomPartsAlerts
)(implicit
    serviceDefinition: ServiceDefinition,
    ctx: ExecutionContext
) extends StackrateLogging {
  private def refFor[T <: PersistentEntity: ClassTag](id: UUID) = eReg.refFor[T](id.toString)

  val started     = System.currentTimeMillis()
  private val log = LoggerFactory.getLogger(classOf[AssemblyListener])

  def str(m: AssemblyStreamMessage): String =
    m match {
      case x => x.getClass.getSimpleName
    }

  def str(m: streams.StreamMessage[AssemblyStreamMessage]): String =
    m match {
      case ValidMessage(value) => s"ValidMessage(${str(value)})"
      case _                   => "InvalidMessage"
    }

  TopicUtils.subscribeLatest(assemblyService.lifecycleTopic(), started, 1) {

    _.payload match {
      case p
          if p.newLifecycle.map(_.name).contains(Analysis) &&
            p.newLifecycle.map(_.status.name).contains(StageStatusName.Success) =>
        val tcmd          = TimelineCommand.system
        val versionEntity = refFor[PCBEntity](p.assemblyReference.version)
        for {
          pcb <- versionEntity.ask(GetVersion(p.assemblyReference.team, p.assemblyReference.version))
          specs <- Future.sequence(pcb.specifications.map { sp =>
            refFor[SpecificationEntity](sp).ask(GetSpecification(p.assemblyReference.team, sp))
          })

          _ <-
            Future.successful(specs.map { spec =>
              SvixHelper.sendInternal(
                PCBSpecificationChanged.EVENT,
                PCBSpecificationChanged(
                  p.assemblyReference.team,
                  spec.id,
                  p.assemblyReference.version,
                  PCBV2Layer.hashString(PCBV2Layer.to(spec).settings)
                )
              )
            })
        } yield Done
    }
  }

  TopicUtils.subscribeLatest(assemblyService.assemblyTopic(), started, 5) { m =>
    m.payload match {
      case ValidMessage(x: FileMessage) =>
        _doFile(x)
      case ValidMessage(x: FileTypesUpdatedMessage) =>
        _doFileTypes(x)
      case ValidMessage(x: FileMatchingMessage) =>
        for {
          _ <- _doFileMatching(x)
          _ <- setFileTypeAlerts(x)
        } yield Done

      case ValidMessage(x: VersionDescription) =>
        _doVersionCreated(x)
      case ValidMessage(x: AssemblyClonedMessage) =>
        _doCloneVersion(x)
    }
  }

  private def _doCloneVersion(message: AssemblyClonedMessage): Future[Done] =
    for {
      _ <- Future.unit

      tcmd            = TimelineCommand.system
      clonedPcbEntity = refFor[PCBEntity](message.assRef.version)

      pcb <- refFor[PCBEntity](message.origAssRef.version).ask(
        GetVersion(
          message.origAssRef.team,
          message.origAssRef.version
        )
      )

      _ <- clonedPcbEntity.ask(
        CloneFromPcbVersion(
          message.assRef.team,
          message.assRef,
          pcb
        )
      )

      resource = getLocalResourceFolder(message.assRef)

      _ <- Future.traverse(pcb.specifications) { specification =>
        val isDefaultSpecification = pcb.defaultSpecification.contains(specification)
        val newId                  = UUID.randomUUID()

        for {
          existing <- refFor[SpecificationEntity](specification).ask(
            GetSpecification(message.origAssRef.team, specification)
          )

          newPreview     = existing.preview.map(_.copy(resource = resource))
          newPreviewRear = existing.previewRear.map(_.copy(resource = resource))

          clonedSpec <- refFor[SpecificationEntity](newId).ask(
            CreateSpecification(
              team = message.assRef.team,
              ass = message.assRef,
              s = existing.copy(
                id = newId,
                assembly = message.assRef,
                status = SpecificationStatus.Changed,
                preview = newPreview,
                previewRear = newPreviewRear
              ),
              tcmd
            )
          )

          _ <- clonedPcbEntity.ask(
            AddSpecification(
              team = message.assRef.team,
              specification = clonedSpec.id,
              default = isDefaultSpecification,
              tcmd = tcmd
            )
          )

          _ <- newPreview match {
            case Some(newPreview) if isDefaultSpecification =>
              assemblyService
                ._setPreview(message.assRef.team, message.assRef.id, Some(message.assRef.version))
                .invoke(newPreview)
            case _ =>
              Future.successful(Done)
          }
        } yield ()
      }
    } yield Done

  private def setFileTypeAlerts(msg: FileMatchingMessage): Future[Done] = {

    def stateForFileType(files: Seq[FileDescription], fileTypes: Seq[String]): CustomPartAlertStatus =
      if (
        files.exists { fd =>
          fileTypes.contains(fd.fType.fileType)
        }
      ) {
        Resolved
      } else {
        Active
      }

    if (msg.locked) {
      val drillState      = stateForFileType(msg.files, LayerConstants.DRILLS)
      val soldermaskState = stateForFileType(msg.files, LayerConstants.SOLDERMASK)
      val silkscreenState = stateForFileType(msg.files, LayerConstants.SILKSCREEN)

      Future.sequence(Seq(
        alerts.upsert(msg.assRef.team, msg.assRef.version, Pcb(NoDrillFile), drillState),
        alerts.upsert(msg.assRef.team, msg.assRef.version, Pcb(NoSoldermask), soldermaskState),
        alerts.upsert(msg.assRef.team, msg.assRef.version, Pcb(NoSilkscreen), silkscreenState)
      )).map(_ => Done)

    } else {
      Future.successful(Done)
    }
  }

  private def _doFileMatching(msg: FileMatchingMessage): Future[Done] = {
    val entity = refFor[PCBEntity](msg.assRef.version)
    for {
      _ <- entity.ask(FinishFileMatching(
        team = msg.assRef.team,
        assRef = msg.assRef,
        stack = msg.files.map(f =>
          GerberFile(
            id = f.fileID,
            name = f.file.name,
            path = f.filePath,
            fType = f.fType,
            metaInfo = None,
            hash = f.file.hash
          )
        )
      ))
    } yield Done
  }

  private def _doFileTypes(msg: FileTypesUpdatedMessage): Future[Done] =
    msg.method match {
      case Updated                     => updateFileTypes(msg.f)
      case Created | Preview | Deleted => Future.successful(Done)
    }

  private def _doFile(msg: FileMessage) = {
    msg.method match {
      case Updated =>
        KamonUtils.parentName("update file types")
        updateFileTypes(msg.f)
      case Created =>
        KamonUtils.parentName("match files and compute hashes")
        matchFiles(msg)
        computeFileHashes(msg)

      case _ =>
    }

    msg.method match {
      case Created =>
        updateFiles(msg).map(_ => Done)

      case Deleted =>
        try
          Future.sequence(msg.f.map { f =>
            refFor[PCBEntity](f.version).ask(
              DeleteFile(
                f.team,
                AssemblyReference(
                  f.team,
                  f.assembly,
                  Some(f.agid),
                  f.version
                ),
                f.fileID
              )
            ).recover {
              case x => Done
            } map (_ => Done)
          }).map(_ => Done)
        catch {
          case e: Throwable =>
            log.warn(s"error deleting file ${msg.f}", e)
            Future.successful(Done)
        }

      case _ => Future.successful(Done)
    }
  }

  @Trace
  private def matchFiles(msg: FileMessage) = {
    val assemblies = msg.f.map(_.assembly).distinct
    val teams      = msg.f.map(_.team).distinct
    val versions   = msg.f.map(_.version).distinct

    if (assemblies.length != 1 || teams.length != 1 || versions.length != 1) {
      throw new TransportException(
        TransportErrorCode.BadRequest,
        s"More than one assembly provided $assemblies, $teams, $versions"
      )
    }

    val scheduled = System.currentTimeMillis()
    logger.info(s"start matching files ${msg.f.map(f => f.file.name + f.fType + f.detectedTypes).mkString(", ")}")
    MatcherCoordinator.submitAll(
      msg.f.filter(_.fType.isMissing),
      types => {
        log.warn(s"[MATCHER]: Matched Files in ${System.currentTimeMillis() - scheduled}ms")
        types.foreachEntry { (fd: FileDescription, tps: Seq[FileType]) =>
          log.warn(
            s"[MATCHER]: Matched File: ${fd.file.name} in assembly ${fd.assembly} in ${System.currentTimeMillis() - scheduled}ms: $tps"
          )
        }

        val f =
          assemblyService._updateFileTypes(teams.head, assemblies.head, Some(versions.head)).invoke(
            FileTypeUpdates(
              updates = types.map(x =>
                FileTypeUpdate(
                  file = x._1.file.name,
                  function = None,
                  detectedTypes = Some(x._2)
                )
              ).toSeq,
              timeline = None
            )
          )

        Await.result(f, 10 minutes)
      }
    )
  }

  @Trace
  private def computeFileHashes(msg: FileMessage): Future[Done] = {
    val assemblies = msg.f.map(_.assembly).distinct
    val teams      = msg.f.map(_.team).distinct
    val versions   = msg.f.map(_.version).distinct

    assert(assemblies.length == 1)
    assert(teams.length == 1)
    assert(versions.length == 1)

    Future
      .traverse(msg.f) { fileDescription =>
        Future {
          blocking {
            logger.info(s"hashing ${fileDescription}")
            val hash = Try(HashUtils.generateSha256Hash(fileDescription.filePath.toJavaFile)) match {
              case Failure(e) =>
                KamonUtils.exceptionError(e, s"error computing hash for ${fileDescription.filePath}")
                None

              case Success(value) => Some(value)
            }

            (fileDescription, hash)
          }
        }.recover {
          case NonFatal(e) =>
            KamonUtils.exceptionError(e, s"error computing hash for ${fileDescription.filePath}")
            (fileDescription, None)
        }
      }.flatMap { filesAndHashes =>
        assemblyService._updateFileHashes(teams.head, assemblies.head, Some(versions.head)).invoke(
          FileHashUpdates(
            updates = filesAndHashes.map {
              case (description, hash) =>
                FileHashUpdate(
                  file = description.file.name,
                  hash = hash,
                  pHash = None
                )
            },
            timeline = None
          )
        )
      }
  }

  private def updateFileTypes(msg: Seq[FileDescription]): Future[Done.type] = {
    val files = msg
      .filter(_.filePath.toJavaFile.exists())
    if (files.length < 1) {
      Future.successful(Done)
    } else {
      Future.sequence(files.groupBy(_.version).values.map { files =>
        val aref = AssemblyReference(
          files.head.team,
          files.head.assembly,
          Some(files.head.agid),
          files.head.version
        )
        refFor[PCBEntity](files.head.version).ask(
          SetFileTypes(
            files.head.team,
            aref,
            toGerberFiles(files)
          )
        ).map(_ => Done)
      }).map(_ => Done)
    }
  }

  //
  private def updateFiles(msg: FileMessage): Future[Done.type] = {
    logger.info(s"start updating files ${msg.f.map(_.file.name).mkString(", ")}")
    val files = msg.f
      .filter(_.fType.service == serviceDefinition.name)
      .filter(_.filePath.toJavaFile.exists())
    logger.info(s"start updating filtered files ${files.map(_.file.name).mkString(", ")}")

    if (files.length < 1) {
      Future.successful(Done)
    } else {

      Future.sequence(files.groupBy(_.version).values.map { files =>
        val aref = AssemblyReference(
          files.head.team,
          files.head.assembly,
          Some(files.head.agid),
          files.head.version
        )

        logger.warn(s"update files ${files} in version ${files.head.version}")
        refFor[PCBEntity](files.head.version).ask(
          SetFiles(
            files.head.team,
            aref,
            toGerberFiles(files)
          )
        ).map(_ => Done)
      }).map(_ => Done)
    }

  }

  private def toGerberFiles(files: Seq[FileDescription]) =
    files.map(f =>
      GerberFile(
        id = f.fileID,
        name = f.file.name,
        path = f.filePath,
        fType = f.file.fType,
        format = None,
        inverted = AssemblyListener.inverted(f),
        metaInfo = None,
        hash = f.file.hash
      )
    )

  private def _doVersionCreated(msg: VersionDescription): Future[Done] =
    try
      doCreatePCBForAssembly(eReg, msg).map(_ => Done)
    catch {
      case e: Throwable =>
        println("failed version creation")
        e.printStackTrace()
        throw e
    }

}

object AssemblyListener extends StackrateLogging {
  def refFor[T <: PersistentEntity: ClassTag](eReg: PersistentEntityRegistry, id: UUID) = eReg.refFor[T](id.toString)

  def doCreatePCBForAssembly(
      eReg: PersistentEntityRegistry,
      msg: VersionDescription
  )(implicit ec: ExecutionContext): Future[(PCBVersion, Option[PCBSpecification])] = {
    val tcmd   = TimelineCommand.system
    val assRef = AssemblyReference(msg.assembly.team, msg.assembly.id, Some(msg.assembly.agid), msg.version.id)

    val entity = refFor[PCBEntity](eReg, msg.version.id)

    entity.ask(GetVersion(msg.assembly.team, assRef.version))
      .flatMap { v =>
        val spec       = PCBSpecification.defaultTemplate(assRef, "default")
        val specentity = refFor[SpecificationEntity](eReg, spec.id)
        logger.info(s"creating pcb for ${v}")
        if (v.assembly.isEmpty) {
          for {
            _ <- entity.ask( // todo copy previous version
              SetVersion(msg.assembly.team, assRef))
            spec <- specentity.ask(CreateSpecification(msg.assembly.team, assRef, spec, tcmd))
            _    <- entity.ask(AddSpecification(msg.assembly.team, spec.id, default = true, tcmd))
            pcb  <- entity.ask(GetVersion(msg.assembly.team, assRef.version))
          } yield (pcb, Some(spec))
        } else {
          // TODO: get specification
          Future.successful((v, None))
        }
      }
  }

  def inverted(f: FileDescription): Option[Boolean] =
    inverted(f.fType)

  def inverted(f: FileType): Option[Boolean] =
    (f.fileType == LayerConstants.SOLDERMASK_TOP ||
      f.fileType == LayerConstants.SOLDERMASK_BOTTOM ||
      f.fileType == LayerConstants.PLANE_MID) match {
      case true  => Some(true)
      case false => None
    }

  def getAutoOutline(pcbv: PCBVersion): Option[(GerberFile, BigDecimal)] = {

    //    val files = filedescs.flatMap(fd => pcbv.files.find(pcbf => pcbf.id == fd.fileID))

    val files = pcbv.files

    files.find(_.fType.fileType == LayerConstants.OUTLINE) match {
      case Some(o) => Some((o, BigDecimal(10000)))
      case None =>
        val files1 = files.filter(f =>
          f.fType.fileType == LayerConstants.MECHANICAL || f.fType.fileType == LayerConstants.KEEP_OUT
        )
        if (files1.nonEmpty) {
          println(s"find best outline in $files1")
          val possibleOutlines = files1.map { p =>
            if (p.format.isDefined) {
              val format = p.format.get
              if (format.dimension.isDefined && format.complexity.isDefined) {

                val dim  = format.dimension.get
                val comp = format.complexity.get
                (p, dim.surfaceArea / (comp * 10)) // get the biggest layer with the lowest complexity
              } else {
                (p, BigDecimal(-1))
              }
            } else {
              (p, BigDecimal(-1))
            }
          }.filter(f => f._2 > 0)
          println(s"possible outlines: ${possibleOutlines}")
          if (possibleOutlines.nonEmpty) {

            val theChosenOne = possibleOutlines.maxBy(_._2)
            println(s"use outline: ${theChosenOne}")
            Some(theChosenOne)
          } else {
            None
          }
        } else {
          None
        }
    }
  }
}
