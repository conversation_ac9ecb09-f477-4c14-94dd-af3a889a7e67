package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.ems.gerber.parser.{G<PERSON>ber<PERSON>arser, GerberParserBaseListener}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.internal.FileType
import de.fellows.utils.spi.GerberFileTypeDetector

import scala.util.Try

/** This [[GerberParserBaseListener Gerber Parser]] runs through the parsed gerber file and collects information about
  * the file type, that may be located in multiple places (according to the
  * [[https://www.ucamco.com/en/gerber/downloads Gerber Format Standard]]):
  *
  * ==Comments==
  * Comments in Gerber (located in `G04` lines) are usually of two formats:
  * ===Normal Comments===
  * These contain various information without any real standard. We try to parse the lines that occur most often and
  * extract the information
  * ===Standard Comments===
  * These Comments start with `G04 #@!` and contain information according to the gerber standard, and usually contain
  * Meta information like `TF` for the file function
  *
  * ==IN Command==
  * This is a deprecated command, nevertheless used relatively often. This sets an arbitrary name for the layer, and
  * often contains keywords suggesting a file type
  *
  * ==TF Command==
  * This is an attribute command that can occur as a command (as well as in a standard comment, if the file tries to
  * conform to older gerber standards) It contains standardised meta data of the files function and can be parsed
  */
class InformationCollector(fileName: String) extends GerberParserBaseListener {

  var physicalOrder: Option[BigDecimal] = None
  var filetype: Option[String]          = None
  var category: Option[String]          = None
  var mime: Option[String]              = None
  var hasExtCommands: Boolean           = false
  var needsExtCommands: Boolean         = false

  var from: Option[Int] = None
  var to: Option[Int]   = None

  var infoConfidence: Int = 0

  val comments = Seq.newBuilder[String]

  val definedApertures = Seq.newBuilder[String]

  override def enterAd(ctx: GerberParser.AdContext): Unit =
    if (ctx.D() != null) {
      val text  = BigDecimal(ctx.name.getText).toString()
      val dCode = s"D${text}"
      definedApertures += dCode
    }

  override def enterLn(ctx: GerberParser.LnContext): Unit = {
    val desc = ctx.getText.drop(2)
   matchInText(desc)
  }

  override def enterG54(ctx: GerberParser.G54Context): Unit = {
    // if there is an aperture selected by a deprecated G54 command, AND the aperture does nit exist,
    // just assume the file is broken by assigning it an unknown file type.
    //
    // This sometimes happens to weird projects, and keeping this as a gerber file will basically break the project
    val aperture = BigDecimal(ctx.integ().getText)
    if (!definedApertures.result().contains(s"D$aperture")) {
      infoConfidence = 10
      category = LayerConstants.Categories.unknown
      filetype = Some(LayerConstants.UNKNOWN)
    }
  }

  override def enterComment(ctx: GerberParser.CommentContext): Unit =
    if (ctx.COMMENTCONTENT() != null) {
      val cmmtcnt = ctx.COMMENTCONTENT().getText
      comments += cmmtcnt

      if (cmmtcnt.trim.startsWith("#@!")) {
        // standardized comment
        parseDefaultComment(cmmtcnt)
      } else {
        // default comment
        parseComment(cmmtcnt)
      }
    }

  override def enterIn(ctx: GerberParser.InContext): Unit =
    Try {
      val intext = ctx.string().getText
      matchInText(intext)
    }

  override def exitGerber(ctx: GerberParser.GerberContext): Unit =
    if (!this.hasExtCommands && this.needsExtCommands) {
      mime = Some(GerberFileTypeDetector.LEGACY_MIME)
      category = Some("unknown")
      filetype = Some(LayerConstants.LEGACY_GERBER)
      infoConfidence = 10
    } else {
      mime = Some("text/gerber")
    }

  override def enterTf(ctx: GerberParser.TfContext): Unit =
    parseDefaultComment(ctx.getText)

  override def enterExt_command(ctx: GerberParser.Ext_commandContext): Unit =
    hasExtCommands = true

  override def enterOp(ctx: GerberParser.OpContext): Unit =
    if (ctx.oper != null) {
      needsExtCommands = true
    }

  def parseDefaultComment(content: String): Unit = {

    // TF.FileFunction: Reference is Ucamco Gerber Reference Chap. 5.6.3
    val tfRegex = "TF\\.FileFunction,(.*)".r

    tfRegex.findAllMatchIn(content).map(_.group(1).split(",")).foreach {
      case x if (x.contains("Drill") || x.contains("Plated") || x.contains("PTH")) =>
        infoConfidence = 4
        category = Some("mechanical")
        filetype =
          Some(
            if (x.contains("PTH")) {
              LayerConstants.PH_DRILL
            } else if (x.contains("NPTH")) {
              LayerConstants.NPH_DRILL
            } else {
              LayerConstants.PH_DRILL
            }
          )

        val ints = x.flatMap(_.toIntOption)
        if (ints.length == 2) {
          from = Some(ints(0))
          to = Some(ints(1))
        }
      //      case x if x(0) == "Drawing" =>

      case x if (x.size >= 3) && x(0) == "Copper" =>
        infoConfidence = 4
        category = Some("gerber")
        filetype = x(2) match {
          case "Top" => Some(LayerConstants.COPPER_TOP)
          case "Inr" => Some(LayerConstants.COPPER_MID)
          case "Bot" => Some(LayerConstants.COPPER_BOTTOM)
          case _     => None
        }
        if (x.lift(3).map(_.toLowerCase).contains("plane")) {
          filetype = Some(LayerConstants.PLANE_MID)
        }

        if (x(1).startsWith("L")) {
          try
            physicalOrder = Some(BigDecimal(x(1).substring(1)))
          catch {
            case _: Throwable =>
          }
        }

      case x if (x.size >= 2) && x(0) == "Soldermask" =>
        infoConfidence = 4
        category = Some("gerber")
        filetype = x(1) match {
          case "Top" => Some(LayerConstants.SOLDERMASK_TOP)
          case "Bot" => Some(LayerConstants.SOLDERMASK_BOTTOM)
          case _     => None
        }
      case x if x.nonEmpty && x(0) == "Pads" =>
        infoConfidence = 4
        category = FileType.UNKNOWN.category
        filetype = Some(FileType.UNKNOWN.fileType)

      case x if (x.size >= 2) && x(0) == "Paste" =>
        infoConfidence = 4
        category = Some("gerber")
        filetype = x(1) match {
          case "Top" => Some(LayerConstants.PASTE_TOP)
          case "Bot" => Some(LayerConstants.PASTE_BOTTOM)
          case _     => None
        }

      case x if x.nonEmpty && x(0) == "Profile" =>
        infoConfidence = 4
        category = Some("mechanical")
        filetype = Some(LayerConstants.OUTLINE)

      case x if x.nonEmpty && (x(0) == "KeepOut" || x(0).toLowerCase == "keep-out") =>
        infoConfidence = 4
        category = Some("mechanical")
        filetype = Some(LayerConstants.KEEP_OUT)

      case x if (x.size >= 2) && x(0) == "Legend" =>
        infoConfidence = 4
        category = Some("gerber")
        filetype = x(1) match {
          case "Top" => Some(LayerConstants.SILKSCREEN_TOP)
          case "Bot" => Some(LayerConstants.SILKSCREEN_BOTTOM)
          case _     => None
        }

      case x if (x.size >= 2) && x(0) == "Other" && (x(1) == "Outline" || x(1) == "BoardOutline") =>
        infoConfidence = 5
        category = Some("mechanical")
        filetype = Some(LayerConstants.OUTLINE)

      case x if (x.size >= 2) && x(0) == "Other" && x(1).contains("Outline") =>
        infoConfidence = 3
        category = Some("mechanical")
        filetype = Some(LayerConstants.OUTLINE)

      case x if (x.size >= 2) && x(0) == "Other" && x(1).startsWith("Mechanical") =>
        infoConfidence = 4
        category = Some("mechanical")
        filetype = Some(LayerConstants.MECHANICAL)

      case x if x.nonEmpty && x(0) == "Other" || x(0).contains("Drawing") =>
        infoConfidence = 3
        category = Some("mechanical")
        filetype = Some(LayerConstants.MECHANICAL)
      case _ =>
    }

  }

  def parseComment(content: String): Unit = {
    val altiumRegex = "Layer_Physical_Order=([0-9]+)".r
    val cadence     = "Layer:\\s*(.*)".r
    val eagleRegex  = "^IN(.*)".r
    // %INBottom Copper*%

    altiumRegex.findAllMatchIn(content).map(_.group(1)).foreach { m =>
      try {
        physicalOrder = Some(BigDecimal(m))
        infoConfidence = 3
      } catch {
        case _: Throwable =>
      }
    }

    val cadenceCopper = "^PIN/.*?L([0-9]+).*?$".r
    val allegroCopper = "^PIN/LAYER([0-9]+).*?$".r
    val cadenceBase   = "^(PIN|VIA_CLASS)/(.*)$".r

    cadence.findAllMatchIn(content).map(_.group(1)).foreach { m =>
      val ids = Map(
        "VIA CLASS/BOTTOM"                   -> ("gerber", LayerConstants.COPPER_BOTTOM),
        "VIA CLASS/TOP"                      -> ("gerber", LayerConstants.COPPER_TOP),
        "VIA CLASS/GND"                      -> ("gerber", LayerConstants.COPPER_MID),
        "VIA CLASS/INT"                      -> ("gerber", LayerConstants.COPPER_MID),
        "VIA CLASS/POWER"                    -> ("gerber", LayerConstants.COPPER_MID),
        "VIA CLASS/SOLDERMASK_BOTTOM"        -> ("gerber", LayerConstants.SOLDERMASK_BOTTOM),
        "VIA CLASS/SOLDERMASK_TOP"           -> ("gerber", LayerConstants.SOLDERMASK_TOP),
        "VIA CLASS/PASTEMASK_BOTTOM"         -> ("gerber", LayerConstants.PASTE_BOTTOM),
        "VIA CLASS/PASTEMASK_TOP"            -> ("gerber", LayerConstants.PASTE_TOP),
        "VIA CLASS/SILKSCREEN_BOTTOM"        -> ("gerber", LayerConstants.SILKSCREEN_BOTTOM),
        "VIA CLASS/SILKSCREEN_TOP"           -> ("gerber", LayerConstants.SILKSCREEN_TOP),
        "PIN/BOTTOM"                         -> ("gerber", LayerConstants.COPPER_BOTTOM),
        "PIN/TOP"                            -> ("gerber", LayerConstants.COPPER_TOP),
        "PIN/GND"                            -> ("gerber", LayerConstants.COPPER_MID),
        "PIN/INT"                            -> ("gerber", LayerConstants.COPPER_MID),
        "PIN/POWER"                          -> ("gerber", LayerConstants.COPPER_MID),
        "PIN/SOLDERMASK_BOTTOM"              -> ("gerber", LayerConstants.SOLDERMASK_BOTTOM),
        "PIN/SOLDERMASK_TOP"                 -> ("gerber", LayerConstants.SOLDERMASK_TOP),
        "PIN/PASTEMASK_BOTTOM"               -> ("gerber", LayerConstants.PASTE_BOTTOM),
        "PIN/PASTEMASK_TOP"                  -> ("gerber", LayerConstants.PASTE_TOP),
        "PIN/SILKSCREEN_BOTTOM"              -> ("gerber", LayerConstants.SILKSCREEN_BOTTOM),
        "PIN/SILKSCREEN_TOP"                 -> ("gerber", LayerConstants.SILKSCREEN_TOP),
        "PACKAGE GEOMETRY/SOLDERMASK_BOTTOM" -> ("gerber", LayerConstants.SOLDERMASK_BOTTOM),
        "PACKAGE GEOMETRY/SOLDERMASK_TOP"    -> ("gerber", LayerConstants.SOLDERMASK_TOP),
        "PACKAGE GEOMETRY/PASTEMASK_BOTTOM"  -> ("gerber", LayerConstants.PASTE_BOTTOM),
        "PACKAGE GEOMETRY/PASTEMASK_TOP"     -> ("gerber", LayerConstants.PASTE_TOP),
        "PACKAGE GEOMETRY/SILKSCREEN_BOTTOM" -> ("gerber", LayerConstants.SILKSCREEN_BOTTOM),
        "PACKAGE GEOMETRY/SILKSCREEN_TOP"    -> ("gerber", LayerConstants.SILKSCREEN_TOP),
        "BOARD GEOMETRY/SOLDERMASK_BOTTOM"   -> ("gerber", LayerConstants.SOLDERMASK_BOTTOM),
        "BOARD GEOMETRY/SOLDERMASK_TOP"      -> ("gerber", LayerConstants.SOLDERMASK_TOP),
        "BOARD GEOMETRY/PASTEMASK_BOTTOM"    -> ("gerber", LayerConstants.PASTE_BOTTOM),
        "BOARD GEOMETRY/PASTEMASK_TOP"       -> ("gerber", LayerConstants.PASTE_TOP),
        "BOARD GEOMETRY/SILKSCREEN_BOTTOM"   -> ("gerber", LayerConstants.SILKSCREEN_BOTTOM),
        "BOARD GEOMETRY/SILKSCREEN_TOP"      -> ("gerber", LayerConstants.SILKSCREEN_TOP),
        "REF DES/SOLDERMASK_BOTTOM"          -> ("gerber", LayerConstants.SOLDERMASK_BOTTOM),
        "REF DES/SOLDERMASK_TOP"             -> ("gerber", LayerConstants.SOLDERMASK_TOP),
        "REF DES/PASTEMASK_BOTTOM"           -> ("gerber", LayerConstants.PASTE_BOTTOM),
        "REF DES/PASTEMASK_TOP"              -> ("gerber", LayerConstants.PASTE_TOP),
        "REF DES/SILKSCREEN_BOTTOM"          -> ("gerber", LayerConstants.SILKSCREEN_BOTTOM),
        "REF DES/SILKSCREEN_TOP"             -> ("gerber", LayerConstants.SILKSCREEN_TOP),
        "BOARD GEOMETRY/OUTLINE"             -> ("mechanical", LayerConstants.OUTLINE),
        "BOARD GEOMETRY/DESIGN_OUTLINE"      -> ("mechanical", LayerConstants.OUTLINE),
        "MANUFACTURING/AUTOSILK_BOTTOM"      -> ("mechanical", LayerConstants.UNKNOWN)
      )

      cadenceBase.findFirstMatchIn(m).foreach { mtch =>
        // this seems to be at least some copper layer, just take a guess at this stage
        if (infoConfidence < 1) {
          infoConfidence = 1
          category = Some("gerber")
          filetype = Some(LayerConstants.COPPER_MID)
        }
      }

      ids.find(p => m.startsWith(p._1)).foreach { p =>
        if (infoConfidence >= 2) {
          if (p._2._1 != "mechanical") {
            infoConfidence = 2
            category = Some(p._2._1)
            filetype = Some(p._2._2)
          }
        } else {
          infoConfidence = 2
          category = Some(p._2._1)
          filetype = Some(p._2._2)
        }

      }

      cadenceCopper.findFirstMatchIn(m).foreach { mtch =>
        infoConfidence = 3
        category = Some("gerber")
        filetype = Some(LayerConstants.COPPER_MID)
        physicalOrder = Some(BigDecimal(mtch.group(1)))
      }

      allegroCopper.findFirstMatchIn(m).foreach { mtch =>
        infoConfidence = 3
        category = Some("gerber")
        filetype = Some(LayerConstants.COPPER_MID)
        physicalOrder = Some(BigDecimal(mtch.group(1)))
      }

    }

    eagleRegex.findAllMatchIn(content).map(_.group(1)).foreach { m =>
      matchInText(m)
    }
  }

  /** Matches IN attributes, which defines the role of the file. it can appear in multiple places in Gerber files
    */
  private def matchInText(m: String) =
    Keywords.getMatchOption(m).flatMap(_.headOption).foreach { opt =>
      infoConfidence = 3
      category = opt match {
        case MechanicalOption(fileType, confidenceModifier) => LayerConstants.Categories.mechanical
        case GerberOption(fileType, confidenceModifier)     => LayerConstants.Categories.gerber
      }
      filetype = Some(opt.fileType)
    }
}
