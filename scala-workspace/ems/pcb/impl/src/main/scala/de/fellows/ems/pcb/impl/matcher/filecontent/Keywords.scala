package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.ems.pcb.model.LayerConstants

import scala.util.matching.Regex

abstract sealed class MatchOption(val fileType: String, val confidenceModifier: Option[Int])

case class MechanicalOption(override val fileType: String, override val confidenceModifier: Option[Int] = None)
    extends MatchOption(fileType, confidenceModifier)

case class GerberOption(override val fileType: String, override val confidenceModifier: Option[Int] = None)
    extends MatchOption(fileType, confidenceModifier)

/** Matches a String */
sealed trait StringMatcher {
  def check(s: String): Option[Seq[MatchOption]]
}

object StringMatcher {
  def apply(a: String, b: MatchOption): StringMatcher = new StrictStringMatcher(a, Seq(b))

  def apply(a: Regex, b: MatchOption): StringMatcher = new RegexMatcher(a, Seq(b))

  def apply(a: String, b: Seq[MatchOption]): StringMatcher = new StrictStringMatcher(a, b)

  def apply(a: Regex, b: Seq[MatchOption]): StringMatcher = new RegexMatcher(a, b)
}

case class RegexMatcher(a: Regex, b: Seq[MatchOption]) extends StringMatcher {
  override def check(s: String): Option[Seq[MatchOption]] =
    if (a.matches(s.toLowerCase)) {
      Some(b)
    } else {
      None
    }
}

/** Checks if a string contains _all_ of the given strings.
  * @param a
  * @param b
  */
case class ContainsStringMatcher(a: Seq[String], b: Seq[MatchOption]) extends StringMatcher {
  override def check(s: String): Option[Seq[MatchOption]] = {
    val toCheck = s.toLowerCase.replace(" ", "")
    if (a.forall(_a => toCheck.contains(_a.toLowerCase.replace(" ", "")))) {
      Some(b)
    } else {
      None
    }
  }
}

object ContainsStringMatcher {
  def apply(a: Seq[String], b: MatchOption): ContainsStringMatcher = new ContainsStringMatcher(a, Seq(b))

  def apply(a: String, b: MatchOption): ContainsStringMatcher = new ContainsStringMatcher(Seq(a), Seq(b))

  def apply(a: Seq[String], b: Seq[MatchOption]): ContainsStringMatcher = new ContainsStringMatcher(a, b)

  def apply(a: String, b: Seq[MatchOption]): ContainsStringMatcher = new ContainsStringMatcher(Seq(a), b)
}

case class StrictStringMatcher(a: String, b: Seq[MatchOption]) extends StringMatcher {
  override def check(s: String): Option[Seq[MatchOption]] =
    if (s.toLowerCase == a.toLowerCase) {
      Some(b)
    } else {
      None
    }
}

object StrictStringMatcher {
  def apply(a: String, b: Seq[MatchOption]): StrictStringMatcher = new StrictStringMatcher(a, b)

  def apply(a: String, b: MatchOption): StrictStringMatcher = new StrictStringMatcher(a, Seq(b))
}

/** Keeps a list of Keywords of filetypes that may indicate a specific filetype.
  *
  * This is intended to be a rather fuzzy match.
  */
object Keywords {
  val INNER_OR_PLANE = Seq(GerberOption(LayerConstants.COPPER_MID), GerberOption(LayerConstants.PLANE_MID, Some(-1)))

  val dictionary = Seq[StringMatcher](
    ContainsStringMatcher(Seq("bottom", "layer"), GerberOption(LayerConstants.COPPER_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "copper"), GerberOption(LayerConstants.COPPER_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "solder"), GerberOption(LayerConstants.SOLDERMASK_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "side"), GerberOption(LayerConstants.COPPER_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "paste"), GerberOption(LayerConstants.PASTE_BOTTOM)),
    ContainsStringMatcher(Seq("rs", "paste"), GerberOption(LayerConstants.PASTE_BOTTOM)),
    ContainsStringMatcher(Seq("top", "paste"), GerberOption(LayerConstants.PASTE_TOP)),
    ContainsStringMatcher(Seq("vs", "paste"), GerberOption(LayerConstants.PASTE_TOP)),
    ContainsStringMatcher(Seq("bottom", "mask"), GerberOption(LayerConstants.SOLDERMASK_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "overlay"), GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "print"), GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "silkscreen"), GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher(Seq("bottom", "peelable"), GerberOption(LayerConstants.PEELABLE_BOTTOM)),
    ContainsStringMatcher(Seq("top", "layer"), GerberOption(LayerConstants.COPPER_TOP)),
    ContainsStringMatcher(Seq("top", "copper"), GerberOption(LayerConstants.COPPER_TOP)),
    ContainsStringMatcher(Seq("top", "solder"), GerberOption(LayerConstants.SOLDERMASK_TOP)),
    ContainsStringMatcher(Seq("top", "side"), GerberOption(LayerConstants.COPPER_TOP)),
    ContainsStringMatcher(Seq("top", "mask"), GerberOption(LayerConstants.SOLDERMASK_TOP)),
    ContainsStringMatcher(Seq("top", "overlay"), GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher(Seq("top", "print"), GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher(Seq("top", "silkscreen"), GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher(Seq("top", "peelable"), GerberOption(LayerConstants.PEELABLE_TOP)),
    ContainsStringMatcher(Seq("copper", "layer"), GerberOption(LayerConstants.COPPER_MID)),
    ContainsStringMatcher("dimension", MechanicalOption(LayerConstants.OUTLINE)),
    ContainsStringMatcher(Seq("top", "stencil"), MechanicalOption(LayerConstants.PASTE_TOP)),
    ContainsStringMatcher(Seq("bottom", "stencil"), MechanicalOption(LayerConstants.PASTE_BOTTOM)),
    ContainsStringMatcher("stencil", MechanicalOption(LayerConstants.PASTE_TOP)),
    ContainsStringMatcher("outline", MechanicalOption(LayerConstants.OUTLINE)),
    ContainsStringMatcher("board dimensions", MechanicalOption(LayerConstants.MECHANICAL)),
    ContainsStringMatcher("aussenkontur", MechanicalOption(LayerConstants.OUTLINE)),
    ContainsStringMatcher("bemassung", MechanicalOption(LayerConstants.MECHANICAL)),
    ContainsStringMatcher("beschriftung", MechanicalOption(LayerConstants.MECHANICAL)),
    ContainsStringMatcher("bestückungsdruck", GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher("peelable", GerberOption(LayerConstants.PEELABLE_TOP)),
    ContainsStringMatcher("inner layer", GerberOption(LayerConstants.COPPER_MID)),
    ContainsStringMatcher("internal gnd", INNER_OR_PLANE),
    ContainsStringMatcher("vcc", INNER_OR_PLANE),
    ContainsStringMatcher("vdd", INNER_OR_PLANE),
    ContainsStringMatcher("internal layer", GerberOption(LayerConstants.COPPER_MID)),
    ContainsStringMatcher("internal plane", INNER_OR_PLANE),
    ContainsStringMatcher("ground plane", INNER_OR_PLANE),
    ContainsStringMatcher("keep out", MechanicalOption(LayerConstants.KEEP_OUT)),
    ContainsStringMatcher("keep-out", MechanicalOption(LayerConstants.KEEP_OUT)),
    ContainsStringMatcher("kontur", MechanicalOption(LayerConstants.OUTLINE)),
    ContainsStringMatcher("sm_top", GerberOption(LayerConstants.SOLDERMASK_TOP)),
    ContainsStringMatcher("sm_bot", GerberOption(LayerConstants.SOLDERMASK_BOTTOM)),
    ContainsStringMatcher("pm_top", GerberOption(LayerConstants.PASTE_TOP)),
    ContainsStringMatcher("pm_bot", GerberOption(LayerConstants.PASTE_BOTTOM)),
    ContainsStringMatcher(Seq("bot", "mask"), GerberOption(LayerConstants.SOLDERMASK_BOTTOM)),
    ContainsStringMatcher(Seq("bot", "overlay"), GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher(Seq("bot", "print"), GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher(Seq("bot", "silkscreen"), GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher(Seq("bot", "peelable"), GerberOption(LayerConstants.PEELABLE_BOTTOM)),
    StringMatcher("abziehlack.b".r, MechanicalOption(LayerConstants.PEELABLE_BOTTOM)),
    StringMatcher("abziehlack.t".r, MechanicalOption(LayerConstants.PEELABLE_TOP)),
    StringMatcher("[0-9]+ top".r, GerberOption(LayerConstants.COPPER_TOP)),
    StringMatcher("[0-9]+ gnd".r, INNER_OR_PLANE),
    StringMatcher("gnd.?[0-9]+".r, INNER_OR_PLANE),
    StringMatcher("[0-9] sig".r, INNER_OR_PLANE),
    StringMatcher("[0-9]+ bot".r, GerberOption(LayerConstants.COPPER_BOTTOM)),
    StrictStringMatcher("bot", GerberOption(LayerConstants.COPPER_BOTTOM)),
    StringMatcher("bottom_[0-9]+".r, GerberOption(LayerConstants.COPPER_BOTTOM)),
    StringMatcher("^bp".r, MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("bdout", MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("blue.?mask".r, MechanicalOption(LayerConstants.MECHANICAL)),
    ContainsStringMatcher("board edge", MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("board out", MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("board.?outline".r, MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("bottom elec", GerberOption(LayerConstants.COPPER_BOTTOM)),
    ContainsStringMatcher("component side", GerberOption(LayerConstants.COPPER_TOP)),
    StringMatcher("core", GerberOption(LayerConstants.COPPER_MID)),
    StringMatcher("core [0-9]+".r, GerberOption(LayerConstants.COPPER_MID)),
    StringMatcher("dielectric.?[0-9]+".r, MechanicalOption(LayerConstants.MECHANICAL)),
    ContainsStringMatcher("gnd plane", INNER_OR_PLANE),
    StringMatcher("gnd_flex", INNER_OR_PLANE),
    StringMatcher("gnd_bot", INNER_OR_PLANE),
    StringMatcher("gnd_top", INNER_OR_PLANE),
    StringMatcher("int[0-9]+ \\(.*\\)".r, INNER_OR_PLANE),
    ContainsStringMatcher("conductor", GerberOption(LayerConstants.COPPER_MID)),
    StringMatcher("layer\\s?([0-9]+)".r, GerberOption(LayerConstants.COPPER_MID)),
    StringMatcher("mid\\s?[0-9]+".r, GerberOption(LayerConstants.COPPER_MID)),
    ContainsStringMatcher("measurement", MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("mech[0-9]+".r, MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("mechanical\\s?([0-9]+)".r, MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("mid[\\s-]?layer\\s?[0-9]+".r, GerberOption(LayerConstants.COPPER_MID)),
    StringMatcher("pwr", INNER_OR_PLANE),
    StringMatcher("pwr plane", INNER_OR_PLANE),
    ContainsStringMatcher("power plane", INNER_OR_PLANE),
    ContainsStringMatcher("power", INNER_OR_PLANE),
    ContainsStringMatcher(Seq("top", "mount"), MechanicalOption(LayerConstants.MECHANICAL)),
    ContainsStringMatcher(Seq("bot", "mount"), MechanicalOption(LayerConstants.MECHANICAL)),
    StringMatcher("paste\\s?mask\\s?bottom".r, GerberOption(LayerConstants.PASTE_BOTTOM)),
    StringMatcher("paste\\s?mask\\s?top", GerberOption(LayerConstants.PASTE_TOP)),
    StringMatcher("profile", MechanicalOption(LayerConstants.OUTLINE)),
    ContainsStringMatcher("signal", GerberOption(LayerConstants.COPPER_MID)),
    ContainsStringMatcher("silkprint top", GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher("bcream", GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher("tcream", GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher("btm", GerberOption(LayerConstants.COPPER_BOTTOM)),
    ContainsStringMatcher("lsmvs", GerberOption(LayerConstants.SOLDERMASK_TOP)),
    ContainsStringMatcher("lsmrs", GerberOption(LayerConstants.SOLDERMASK_BOTTOM)),
    ContainsStringMatcher("sevs", GerberOption(LayerConstants.SILKSCREEN_TOP)),
    ContainsStringMatcher("sers", GerberOption(LayerConstants.SILKSCREEN_BOTTOM)),
    ContainsStringMatcher("vs", GerberOption(LayerConstants.COPPER_TOP)),
    ContainsStringMatcher("rs", GerberOption(LayerConstants.COPPER_BOTTOM)),

    // last resorts
    StringMatcher("^l[0-9]+".r, GerberOption(LayerConstants.COPPER_MID)),
    StringMatcher("^v[0-9]+".r, GerberOption(LayerConstants.COPPER_MID))
  )

  /** Get matched options for a String. Only returns the matches for the first Keyword that applies.
    */
  def getMatchOption(c: String): Option[Seq[MatchOption]] =
    dictionary
      .view                // use as a view to stop at the first result
      .flatMap(_.check(c)) // map to the result
      .headOption          // take the first result
}

