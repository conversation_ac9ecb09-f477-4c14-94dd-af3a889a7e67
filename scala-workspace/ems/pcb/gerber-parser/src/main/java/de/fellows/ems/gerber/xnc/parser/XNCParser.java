// Generated from /home/<USER>/src/epibator/scala-workspace/ems/pcb/gerber-parser/src/main/antlr4/XNCParser.g4 by ANTLR 4.13.1
package de.fellows.ems.gerber.xnc.parser;

import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.ATN;
import org.antlr.v4.runtime.atn.ATNDeserializer;
import org.antlr.v4.runtime.atn.ParserATNSimulator;
import org.antlr.v4.runtime.atn.PredictionContextCache;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.tree.ParseTreeListener;
import org.antlr.v4.runtime.tree.TerminalNode;

import java.util.List;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue"})
public class XNCParser extends Parser {
	static { RuntimeMetaData.checkVersion("4.13.1", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		M48=1, M30=2, G05=3, G01=4, G02=5, G03=6, G00=7, M15=8, M16=9, EOH=10, 
		LZ=11, TZ=12, DOT=13, COMMA=14, T=15, C=16, X=17, Y=18, A=19, S=20, F=21, 
		B=22, COMMENT=23, INCH=24, METRIC=25, NEWLINE=26, DIGIT=27, POSDIGIT=28, 
		COMMENTCONTENT=29, STRING=30, CLOSE=31;
	public static final int
		RULE_xnc = 0, RULE_header = 1, RULE_toolTable = 2, RULE_body = 3, RULE_drillSelection = 4, 
		RULE_routSelection = 5, RULE_rout = 6, RULE_comment = 7, RULE_startHeader = 8, 
		RULE_setUnit = 9, RULE_toolDeclaration = 10, RULE_endHeader = 11, RULE_drillMode = 12, 
		RULE_setRoutMode = 13, RULE_selectTool = 14, RULE_drillHit = 15, RULE_toolDown = 16, 
		RULE_toolUp = 17, RULE_linearRout = 18, RULE_cwRout = 19, RULE_ccwRout = 20, 
		RULE_toolNumber = 21, RULE_holeDiameter = 22, RULE_decimal = 23, RULE_integer = 24;
	private static String[] makeRuleNames() {
		return new String[] {
			"xnc", "header", "toolTable", "body", "drillSelection", "routSelection", 
			"rout", "comment", "startHeader", "setUnit", "toolDeclaration", "endHeader", 
			"drillMode", "setRoutMode", "selectTool", "drillHit", "toolDown", "toolUp", 
			"linearRout", "cwRout", "ccwRout", "toolNumber", "holeDiameter", "decimal", 
			"integer"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'M48'", "'M30'", "'G05'", "'G01'", "'G02'", "'G03'", "'G00'", 
			"'M15'", "'M16'", "'%'", "'LZ'", "'TZ'", "'.'", "','", "'T'", "'C'", 
			"'X'", "'Y'", "'A'", "'S'", "'F'", "'B'", "';'", "'INCH'", "'METRIC'"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, "M48", "M30", "G05", "G01", "G02", "G03", "G00", "M15", "M16", 
			"EOH", "LZ", "TZ", "DOT", "COMMA", "T", "C", "X", "Y", "A", "S", "F", 
			"B", "COMMENT", "INCH", "METRIC", "NEWLINE", "DIGIT", "POSDIGIT", "COMMENTCONTENT", 
			"STRING", "CLOSE"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}

	@Override
	public String getGrammarFileName() { return "XNCParser.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public ATN getATN() { return _ATN; }

	public XNCParser(TokenStream input) {
		super(input);
		_interp = new ParserATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@SuppressWarnings("CheckReturnValue")
	public static class XncContext extends ParserRuleContext {
		public HeaderContext header() {
			return getRuleContext(HeaderContext.class,0);
		}
		public BodyContext body() {
			return getRuleContext(BodyContext.class,0);
		}
		public TerminalNode M30() { return getToken(XNCParser.M30, 0); }
		public List<TerminalNode> NEWLINE() { return getTokens(XNCParser.NEWLINE); }
		public TerminalNode NEWLINE(int i) {
			return getToken(XNCParser.NEWLINE, i);
		}
		public XncContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_xnc; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterXnc(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitXnc(this);
		}
	}

	public final XncContext xnc() throws RecognitionException {
		XncContext _localctx = new XncContext(_ctx, getState());
		enterRule(_localctx, 0, RULE_xnc);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(50);
			header();
			setState(51);
			body();
			setState(52);
			match(M30);
			setState(56);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==NEWLINE) {
				{
				{
				setState(53);
				match(NEWLINE);
				}
				}
				setState(58);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class HeaderContext extends ParserRuleContext {
		public StartHeaderContext startHeader() {
			return getRuleContext(StartHeaderContext.class,0);
		}
		public SetUnitContext setUnit() {
			return getRuleContext(SetUnitContext.class,0);
		}
		public ToolTableContext toolTable() {
			return getRuleContext(ToolTableContext.class,0);
		}
		public EndHeaderContext endHeader() {
			return getRuleContext(EndHeaderContext.class,0);
		}
		public List<CommentContext> comment() {
			return getRuleContexts(CommentContext.class);
		}
		public CommentContext comment(int i) {
			return getRuleContext(CommentContext.class,i);
		}
		public HeaderContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_header; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterHeader(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitHeader(this);
		}
	}

	public final HeaderContext header() throws RecognitionException {
		HeaderContext _localctx = new HeaderContext(_ctx, getState());
		enterRule(_localctx, 2, RULE_header);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(62);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==COMMENT) {
				{
				{
				setState(59);
				comment();
				}
				}
				setState(64);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(65);
			startHeader();
			setState(69);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==COMMENT) {
				{
				{
				setState(66);
				comment();
				}
				}
				setState(71);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			setState(72);
			setUnit();
			setState(73);
			toolTable();
			setState(74);
			endHeader();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ToolTableContext extends ParserRuleContext {
		public List<ToolDeclarationContext> toolDeclaration() {
			return getRuleContexts(ToolDeclarationContext.class);
		}
		public ToolDeclarationContext toolDeclaration(int i) {
			return getRuleContext(ToolDeclarationContext.class,i);
		}
		public List<CommentContext> comment() {
			return getRuleContexts(CommentContext.class);
		}
		public CommentContext comment(int i) {
			return getRuleContext(CommentContext.class,i);
		}
		public ToolTableContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_toolTable; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterToolTable(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitToolTable(this);
		}
	}

	public final ToolTableContext toolTable() throws RecognitionException {
		ToolTableContext _localctx = new ToolTableContext(_ctx, getState());
		enterRule(_localctx, 4, RULE_toolTable);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(80);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while (_la==T || _la==COMMENT) {
				{
				setState(78);
				_errHandler.sync(this);
				switch (_input.LA(1)) {
				case T:
					{
					setState(76);
					toolDeclaration();
					}
					break;
				case COMMENT:
					{
					setState(77);
					comment();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				}
				setState(82);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class BodyContext extends ParserRuleContext {
		public List<DrillSelectionContext> drillSelection() {
			return getRuleContexts(DrillSelectionContext.class);
		}
		public DrillSelectionContext drillSelection(int i) {
			return getRuleContext(DrillSelectionContext.class,i);
		}
		public List<RoutSelectionContext> routSelection() {
			return getRuleContexts(RoutSelectionContext.class);
		}
		public RoutSelectionContext routSelection(int i) {
			return getRuleContext(RoutSelectionContext.class,i);
		}
		public List<CommentContext> comment() {
			return getRuleContexts(CommentContext.class);
		}
		public CommentContext comment(int i) {
			return getRuleContext(CommentContext.class,i);
		}
		public BodyContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_body; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterBody(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitBody(this);
		}
	}

	public final BodyContext body() throws RecognitionException {
		BodyContext _localctx = new BodyContext(_ctx, getState());
		enterRule(_localctx, 6, RULE_body);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(88);
			_errHandler.sync(this);
			_la = _input.LA(1);
			while ((((_la) & ~0x3f) == 0 && ((1L << _la) & 8421512L) != 0)) {
				{
				setState(86);
				_errHandler.sync(this);
				switch (_input.LA(1)) {
				case G05:
				case T:
					{
					setState(83);
					drillSelection();
					}
					break;
				case G00:
					{
					setState(84);
					routSelection();
					}
					break;
				case COMMENT:
					{
					setState(85);
					comment();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				}
				setState(90);
				_errHandler.sync(this);
				_la = _input.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class DrillSelectionContext extends ParserRuleContext {
		public DrillModeContext drillMode() {
			return getRuleContext(DrillModeContext.class,0);
		}
		public List<SelectToolContext> selectTool() {
			return getRuleContexts(SelectToolContext.class);
		}
		public SelectToolContext selectTool(int i) {
			return getRuleContext(SelectToolContext.class,i);
		}
		public List<DrillHitContext> drillHit() {
			return getRuleContexts(DrillHitContext.class);
		}
		public DrillHitContext drillHit(int i) {
			return getRuleContext(DrillHitContext.class,i);
		}
		public DrillSelectionContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_drillSelection; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterDrillSelection(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitDrillSelection(this);
		}
	}

	public final DrillSelectionContext drillSelection() throws RecognitionException {
		DrillSelectionContext _localctx = new DrillSelectionContext(_ctx, getState());
		enterRule(_localctx, 8, RULE_drillSelection);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(92);
			_errHandler.sync(this);
			_la = _input.LA(1);
			if (_la==G05) {
				{
				setState(91);
				drillMode();
				}
			}

			setState(101); 
			_errHandler.sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					setState(94);
					selectTool();
					{
					setState(98);
					_errHandler.sync(this);
					_la = _input.LA(1);
					while ((((_la) & ~0x3f) == 0 && ((1L << _la) & 67502080L) != 0)) {
						{
						{
						setState(95);
						drillHit();
						}
						}
						setState(100);
						_errHandler.sync(this);
						_la = _input.LA(1);
					}
					}
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(103); 
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,9,_ctx);
			} while ( _alt!=2 && _alt!= ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class RoutSelectionContext extends ParserRuleContext {
		public SetRoutModeContext setRoutMode() {
			return getRuleContext(SetRoutModeContext.class,0);
		}
		public List<SelectToolContext> selectTool() {
			return getRuleContexts(SelectToolContext.class);
		}
		public SelectToolContext selectTool(int i) {
			return getRuleContext(SelectToolContext.class,i);
		}
		public List<ToolDownContext> toolDown() {
			return getRuleContexts(ToolDownContext.class);
		}
		public ToolDownContext toolDown(int i) {
			return getRuleContext(ToolDownContext.class,i);
		}
		public List<ToolUpContext> toolUp() {
			return getRuleContexts(ToolUpContext.class);
		}
		public ToolUpContext toolUp(int i) {
			return getRuleContext(ToolUpContext.class,i);
		}
		public List<RoutContext> rout() {
			return getRuleContexts(RoutContext.class);
		}
		public RoutContext rout(int i) {
			return getRuleContext(RoutContext.class,i);
		}
		public RoutSelectionContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_routSelection; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterRoutSelection(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitRoutSelection(this);
		}
	}

	public final RoutSelectionContext routSelection() throws RecognitionException {
		RoutSelectionContext _localctx = new RoutSelectionContext(_ctx, getState());
		enterRule(_localctx, 10, RULE_routSelection);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(105);
			setRoutMode();
			setState(118);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,11,_ctx);
			while ( _alt!=2 && _alt!= ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					setState(106);
					selectTool();
					setState(107);
					toolDown();
					setState(111);
					_errHandler.sync(this);
					_la = _input.LA(1);
					while ((((_la) & ~0x3f) == 0 && ((1L << _la) & 112L) != 0)) {
						{
						{
						setState(108);
						rout();
						}
						}
						setState(113);
						_errHandler.sync(this);
						_la = _input.LA(1);
					}
					setState(114);
					toolUp();
					}
					} 
				}
				setState(120);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,11,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class RoutContext extends ParserRuleContext {
		public LinearRoutContext linearRout() {
			return getRuleContext(LinearRoutContext.class,0);
		}
		public CwRoutContext cwRout() {
			return getRuleContext(CwRoutContext.class,0);
		}
		public CcwRoutContext ccwRout() {
			return getRuleContext(CcwRoutContext.class,0);
		}
		public RoutContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_rout; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterRout(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitRout(this);
		}
	}

	public final RoutContext rout() throws RecognitionException {
		RoutContext _localctx = new RoutContext(_ctx, getState());
		enterRule(_localctx, 12, RULE_rout);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(124);
			_errHandler.sync(this);
			switch (_input.LA(1)) {
			case G01:
				{
				setState(121);
				linearRout();
				}
				break;
			case G02:
				{
				setState(122);
				cwRout();
				}
				break;
			case G03:
				{
				setState(123);
				ccwRout();
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class CommentContext extends ParserRuleContext {
		public TerminalNode COMMENT() { return getToken(XNCParser.COMMENT, 0); }
		public TerminalNode CLOSE() { return getToken(XNCParser.CLOSE, 0); }
		public TerminalNode COMMENTCONTENT() { return getToken(XNCParser.COMMENTCONTENT, 0); }
		public CommentContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_comment; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterComment(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitComment(this);
		}
	}

	public final CommentContext comment() throws RecognitionException {
		CommentContext _localctx = new CommentContext(_ctx, getState());
		enterRule(_localctx, 14, RULE_comment);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(126);
			match(COMMENT);
			setState(128);
			_errHandler.sync(this);
			_la = _input.LA(1);
			if (_la==COMMENTCONTENT) {
				{
				setState(127);
				match(COMMENTCONTENT);
				}
			}

			setState(130);
			match(CLOSE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class StartHeaderContext extends ParserRuleContext {
		public TerminalNode M48() { return getToken(XNCParser.M48, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public StartHeaderContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_startHeader; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterStartHeader(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitStartHeader(this);
		}
	}

	public final StartHeaderContext startHeader() throws RecognitionException {
		StartHeaderContext _localctx = new StartHeaderContext(_ctx, getState());
		enterRule(_localctx, 16, RULE_startHeader);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(132);
			match(M48);
			setState(133);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class SetUnitContext extends ParserRuleContext {
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public TerminalNode INCH() { return getToken(XNCParser.INCH, 0); }
		public TerminalNode METRIC() { return getToken(XNCParser.METRIC, 0); }
		public SetUnitContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_setUnit; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterSetUnit(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitSetUnit(this);
		}
	}

	public final SetUnitContext setUnit() throws RecognitionException {
		SetUnitContext _localctx = new SetUnitContext(_ctx, getState());
		enterRule(_localctx, 18, RULE_setUnit);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(135);
			_la = _input.LA(1);
			if ( !(_la==INCH || _la==METRIC) ) {
			_errHandler.recoverInline(this);
			}
			else {
				if ( _input.LA(1)==Token.EOF ) matchedEOF = true;
				_errHandler.reportMatch(this);
				consume();
			}
			setState(136);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ToolDeclarationContext extends ParserRuleContext {
		public TerminalNode T() { return getToken(XNCParser.T, 0); }
		public ToolNumberContext toolNumber() {
			return getRuleContext(ToolNumberContext.class,0);
		}
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public List<TerminalNode> C() { return getTokens(XNCParser.C); }
		public TerminalNode C(int i) {
			return getToken(XNCParser.C, i);
		}
		public List<HoleDiameterContext> holeDiameter() {
			return getRuleContexts(HoleDiameterContext.class);
		}
		public HoleDiameterContext holeDiameter(int i) {
			return getRuleContext(HoleDiameterContext.class,i);
		}
		public List<TerminalNode> S() { return getTokens(XNCParser.S); }
		public TerminalNode S(int i) {
			return getToken(XNCParser.S, i);
		}
		public List<DecimalContext> decimal() {
			return getRuleContexts(DecimalContext.class);
		}
		public DecimalContext decimal(int i) {
			return getRuleContext(DecimalContext.class,i);
		}
		public List<TerminalNode> B() { return getTokens(XNCParser.B); }
		public TerminalNode B(int i) {
			return getToken(XNCParser.B, i);
		}
		public List<TerminalNode> F() { return getTokens(XNCParser.F); }
		public TerminalNode F(int i) {
			return getToken(XNCParser.F, i);
		}
		public ToolDeclarationContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_toolDeclaration; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterToolDeclaration(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitToolDeclaration(this);
		}
	}

	public final ToolDeclarationContext toolDeclaration() throws RecognitionException {
		ToolDeclarationContext _localctx = new ToolDeclarationContext(_ctx, getState());
		enterRule(_localctx, 20, RULE_toolDeclaration);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(138);
			match(T);
			setState(139);
			toolNumber();
			setState(148); 
			_errHandler.sync(this);
			_la = _input.LA(1);
			do {
				{
				setState(148);
				_errHandler.sync(this);
				switch (_input.LA(1)) {
				case C:
					{
					{
					setState(140);
					match(C);
					setState(141);
					holeDiameter();
					}
					}
					break;
				case S:
					{
					{
					setState(142);
					match(S);
					setState(143);
					decimal();
					}
					}
					break;
				case B:
					{
					{
					setState(144);
					match(B);
					setState(145);
					decimal();
					}
					}
					break;
				case F:
					{
					{
					setState(146);
					match(F);
					setState(147);
					decimal();
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				}
				setState(150); 
				_errHandler.sync(this);
				_la = _input.LA(1);
			} while ( (((_la) & ~0x3f) == 0 && ((1L << _la) & 7405568L) != 0) );
			setState(152);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class EndHeaderContext extends ParserRuleContext {
		public TerminalNode EOH() { return getToken(XNCParser.EOH, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public EndHeaderContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_endHeader; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterEndHeader(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitEndHeader(this);
		}
	}

	public final EndHeaderContext endHeader() throws RecognitionException {
		EndHeaderContext _localctx = new EndHeaderContext(_ctx, getState());
		enterRule(_localctx, 22, RULE_endHeader);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(154);
			match(EOH);
			setState(155);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class DrillModeContext extends ParserRuleContext {
		public TerminalNode G05() { return getToken(XNCParser.G05, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public DrillModeContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_drillMode; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterDrillMode(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitDrillMode(this);
		}
	}

	public final DrillModeContext drillMode() throws RecognitionException {
		DrillModeContext _localctx = new DrillModeContext(_ctx, getState());
		enterRule(_localctx, 24, RULE_drillMode);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(157);
			match(G05);
			setState(158);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class SetRoutModeContext extends ParserRuleContext {
		public TerminalNode G00() { return getToken(XNCParser.G00, 0); }
		public TerminalNode X() { return getToken(XNCParser.X, 0); }
		public List<DecimalContext> decimal() {
			return getRuleContexts(DecimalContext.class);
		}
		public DecimalContext decimal(int i) {
			return getRuleContext(DecimalContext.class,i);
		}
		public TerminalNode Y() { return getToken(XNCParser.Y, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public SetRoutModeContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_setRoutMode; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterSetRoutMode(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitSetRoutMode(this);
		}
	}

	public final SetRoutModeContext setRoutMode() throws RecognitionException {
		SetRoutModeContext _localctx = new SetRoutModeContext(_ctx, getState());
		enterRule(_localctx, 26, RULE_setRoutMode);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(160);
			match(G00);
			setState(161);
			match(X);
			setState(162);
			decimal();
			setState(163);
			match(Y);
			setState(164);
			decimal();
			setState(165);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class SelectToolContext extends ParserRuleContext {
		public TerminalNode T() { return getToken(XNCParser.T, 0); }
		public ToolNumberContext toolNumber() {
			return getRuleContext(ToolNumberContext.class,0);
		}
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public SelectToolContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_selectTool; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterSelectTool(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitSelectTool(this);
		}
	}

	public final SelectToolContext selectTool() throws RecognitionException {
		SelectToolContext _localctx = new SelectToolContext(_ctx, getState());
		enterRule(_localctx, 28, RULE_selectTool);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(167);
			match(T);
			setState(168);
			toolNumber();
			setState(169);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class DrillHitContext extends ParserRuleContext {
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public TerminalNode X() { return getToken(XNCParser.X, 0); }
		public List<DecimalContext> decimal() {
			return getRuleContexts(DecimalContext.class);
		}
		public DecimalContext decimal(int i) {
			return getRuleContext(DecimalContext.class,i);
		}
		public TerminalNode Y() { return getToken(XNCParser.Y, 0); }
		public DrillHitContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_drillHit; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterDrillHit(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitDrillHit(this);
		}
	}

	public final DrillHitContext drillHit() throws RecognitionException {
		DrillHitContext _localctx = new DrillHitContext(_ctx, getState());
		enterRule(_localctx, 30, RULE_drillHit);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(173);
			_errHandler.sync(this);
			_la = _input.LA(1);
			if (_la==X) {
				{
				setState(171);
				match(X);
				setState(172);
				decimal();
				}
			}

			setState(177);
			_errHandler.sync(this);
			_la = _input.LA(1);
			if (_la==Y) {
				{
				setState(175);
				match(Y);
				setState(176);
				decimal();
				}
			}

			setState(179);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ToolDownContext extends ParserRuleContext {
		public TerminalNode M15() { return getToken(XNCParser.M15, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public ToolDownContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_toolDown; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterToolDown(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitToolDown(this);
		}
	}

	public final ToolDownContext toolDown() throws RecognitionException {
		ToolDownContext _localctx = new ToolDownContext(_ctx, getState());
		enterRule(_localctx, 32, RULE_toolDown);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(181);
			match(M15);
			setState(182);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ToolUpContext extends ParserRuleContext {
		public TerminalNode M16() { return getToken(XNCParser.M16, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public ToolUpContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_toolUp; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterToolUp(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitToolUp(this);
		}
	}

	public final ToolUpContext toolUp() throws RecognitionException {
		ToolUpContext _localctx = new ToolUpContext(_ctx, getState());
		enterRule(_localctx, 34, RULE_toolUp);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(184);
			match(M16);
			setState(185);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class LinearRoutContext extends ParserRuleContext {
		public TerminalNode G01() { return getToken(XNCParser.G01, 0); }
		public TerminalNode X() { return getToken(XNCParser.X, 0); }
		public List<DecimalContext> decimal() {
			return getRuleContexts(DecimalContext.class);
		}
		public DecimalContext decimal(int i) {
			return getRuleContext(DecimalContext.class,i);
		}
		public TerminalNode Y() { return getToken(XNCParser.Y, 0); }
		public TerminalNode NEWLINE() { return getToken(XNCParser.NEWLINE, 0); }
		public LinearRoutContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_linearRout; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterLinearRout(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitLinearRout(this);
		}
	}

	public final LinearRoutContext linearRout() throws RecognitionException {
		LinearRoutContext _localctx = new LinearRoutContext(_ctx, getState());
		enterRule(_localctx, 36, RULE_linearRout);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(187);
			match(G01);
			setState(188);
			match(X);
			setState(189);
			decimal();
			setState(190);
			match(Y);
			setState(191);
			decimal();
			setState(192);
			match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class CwRoutContext extends ParserRuleContext {
		public TerminalNode G02() { return getToken(XNCParser.G02, 0); }
		public TerminalNode X() { return getToken(XNCParser.X, 0); }
		public List<DecimalContext> decimal() {
			return getRuleContexts(DecimalContext.class);
		}
		public DecimalContext decimal(int i) {
			return getRuleContext(DecimalContext.class,i);
		}
		public TerminalNode Y() { return getToken(XNCParser.Y, 0); }
		public TerminalNode A() { return getToken(XNCParser.A, 0); }
		public CwRoutContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_cwRout; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterCwRout(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitCwRout(this);
		}
	}

	public final CwRoutContext cwRout() throws RecognitionException {
		CwRoutContext _localctx = new CwRoutContext(_ctx, getState());
		enterRule(_localctx, 38, RULE_cwRout);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(194);
			match(G02);
			setState(195);
			match(X);
			setState(196);
			decimal();
			setState(197);
			match(Y);
			setState(198);
			decimal();
			setState(199);
			match(A);
			setState(200);
			decimal();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class CcwRoutContext extends ParserRuleContext {
		public TerminalNode G03() { return getToken(XNCParser.G03, 0); }
		public TerminalNode X() { return getToken(XNCParser.X, 0); }
		public List<DecimalContext> decimal() {
			return getRuleContexts(DecimalContext.class);
		}
		public DecimalContext decimal(int i) {
			return getRuleContext(DecimalContext.class,i);
		}
		public TerminalNode Y() { return getToken(XNCParser.Y, 0); }
		public TerminalNode A() { return getToken(XNCParser.A, 0); }
		public CcwRoutContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_ccwRout; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterCcwRout(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitCcwRout(this);
		}
	}

	public final CcwRoutContext ccwRout() throws RecognitionException {
		CcwRoutContext _localctx = new CcwRoutContext(_ctx, getState());
		enterRule(_localctx, 40, RULE_ccwRout);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(202);
			match(G03);
			setState(203);
			match(X);
			setState(204);
			decimal();
			setState(205);
			match(Y);
			setState(206);
			decimal();
			setState(207);
			match(A);
			setState(208);
			decimal();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class ToolNumberContext extends ParserRuleContext {
		public List<TerminalNode> DIGIT() { return getTokens(XNCParser.DIGIT); }
		public TerminalNode DIGIT(int i) {
			return getToken(XNCParser.DIGIT, i);
		}
		public ToolNumberContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_toolNumber; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterToolNumber(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitToolNumber(this);
		}
	}

	public final ToolNumberContext toolNumber() throws RecognitionException {
		ToolNumberContext _localctx = new ToolNumberContext(_ctx, getState());
		enterRule(_localctx, 42, RULE_toolNumber);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(211); 
			_errHandler.sync(this);
			_la = _input.LA(1);
			do {
				{
				{
				setState(210);
				match(DIGIT);
				}
				}
				setState(213); 
				_errHandler.sync(this);
				_la = _input.LA(1);
			} while ( _la==DIGIT );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class HoleDiameterContext extends ParserRuleContext {
		public DecimalContext decimal() {
			return getRuleContext(DecimalContext.class,0);
		}
		public HoleDiameterContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_holeDiameter; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterHoleDiameter(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitHoleDiameter(this);
		}
	}

	public final HoleDiameterContext holeDiameter() throws RecognitionException {
		HoleDiameterContext _localctx = new HoleDiameterContext(_ctx, getState());
		enterRule(_localctx, 44, RULE_holeDiameter);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(215);
			decimal();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class DecimalContext extends ParserRuleContext {
		public List<IntegerContext> integer() {
			return getRuleContexts(IntegerContext.class);
		}
		public IntegerContext integer(int i) {
			return getRuleContext(IntegerContext.class,i);
		}
		public TerminalNode DOT() { return getToken(XNCParser.DOT, 0); }
		public DecimalContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_decimal; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterDecimal(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitDecimal(this);
		}
	}

	public final DecimalContext decimal() throws RecognitionException {
		DecimalContext _localctx = new DecimalContext(_ctx, getState());
		enterRule(_localctx, 46, RULE_decimal);
		try {
			setState(223);
			_errHandler.sync(this);
			switch ( getInterpreter().adaptivePredict(_input,19,_ctx) ) {
			case 1:
				enterOuterAlt(_localctx, 1);
				{
				}
				break;
			case 2:
				enterOuterAlt(_localctx, 2);
				{
				setState(218);
				integer();
				setState(219);
				match(DOT);
				setState(220);
				integer();
				}
				break;
			case 3:
				enterOuterAlt(_localctx, 3);
				{
				setState(222);
				integer();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	@SuppressWarnings("CheckReturnValue")
	public static class IntegerContext extends ParserRuleContext {
		public List<TerminalNode> DIGIT() { return getTokens(XNCParser.DIGIT); }
		public TerminalNode DIGIT(int i) {
			return getToken(XNCParser.DIGIT, i);
		}
		public IntegerContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_integer; }
		@Override
		public void enterRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).enterInteger(this);
		}
		@Override
		public void exitRule(ParseTreeListener listener) {
			if ( listener instanceof XNCParserListener) ((XNCParserListener)listener).exitInteger(this);
		}
	}

	public final IntegerContext integer() throws RecognitionException {
		IntegerContext _localctx = new IntegerContext(_ctx, getState());
		enterRule(_localctx, 48, RULE_integer);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(226); 
			_errHandler.sync(this);
			_la = _input.LA(1);
			do {
				{
				{
				setState(225);
				match(DIGIT);
				}
				}
				setState(228); 
				_errHandler.sync(this);
				_la = _input.LA(1);
			} while ( _la==DIGIT );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	public static final String _serializedATN =
		"\u0004\u0001\u001f\u00e7\u0002\u0000\u0007\u0000\u0002\u0001\u0007\u0001"+
		"\u0002\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004\u0007\u0004"+
		"\u0002\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007\u0007\u0007"+
		"\u0002\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b\u0007\u000b"+
		"\u0002\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002\u000f\u0007"+
		"\u000f\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002\u0012\u0007"+
		"\u0012\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002\u0015\u0007"+
		"\u0015\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002\u0018\u0007"+
		"\u0018\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0005\u00007\b"+
		"\u0000\n\u0000\f\u0000:\t\u0000\u0001\u0001\u0005\u0001=\b\u0001\n\u0001"+
		"\f\u0001@\t\u0001\u0001\u0001\u0001\u0001\u0005\u0001D\b\u0001\n\u0001"+
		"\f\u0001G\t\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001"+
		"\u0002\u0001\u0002\u0005\u0002O\b\u0002\n\u0002\f\u0002R\t\u0002\u0001"+
		"\u0003\u0001\u0003\u0001\u0003\u0005\u0003W\b\u0003\n\u0003\f\u0003Z\t"+
		"\u0003\u0001\u0004\u0003\u0004]\b\u0004\u0001\u0004\u0001\u0004\u0005"+
		"\u0004a\b\u0004\n\u0004\f\u0004d\t\u0004\u0004\u0004f\b\u0004\u000b\u0004"+
		"\f\u0004g\u0001\u0005\u0001\u0005\u0001\u0005\u0001\u0005\u0005\u0005"+
		"n\b\u0005\n\u0005\f\u0005q\t\u0005\u0001\u0005\u0001\u0005\u0005\u0005"+
		"u\b\u0005\n\u0005\f\u0005x\t\u0005\u0001\u0006\u0001\u0006\u0001\u0006"+
		"\u0003\u0006}\b\u0006\u0001\u0007\u0001\u0007\u0003\u0007\u0081\b\u0007"+
		"\u0001\u0007\u0001\u0007\u0001\b\u0001\b\u0001\b\u0001\t\u0001\t\u0001"+
		"\t\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001\n\u0001"+
		"\n\u0001\n\u0004\n\u0095\b\n\u000b\n\f\n\u0096\u0001\n\u0001\n\u0001\u000b"+
		"\u0001\u000b\u0001\u000b\u0001\f\u0001\f\u0001\f\u0001\r\u0001\r\u0001"+
		"\r\u0001\r\u0001\r\u0001\r\u0001\r\u0001\u000e\u0001\u000e\u0001\u000e"+
		"\u0001\u000e\u0001\u000f\u0001\u000f\u0003\u000f\u00ae\b\u000f\u0001\u000f"+
		"\u0001\u000f\u0003\u000f\u00b2\b\u000f\u0001\u000f\u0001\u000f\u0001\u0010"+
		"\u0001\u0010\u0001\u0010\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0012"+
		"\u0001\u0012\u0001\u0012\u0001\u0012\u0001\u0012\u0001\u0012\u0001\u0012"+
		"\u0001\u0013\u0001\u0013\u0001\u0013\u0001\u0013\u0001\u0013\u0001\u0013"+
		"\u0001\u0013\u0001\u0013\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0014"+
		"\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0015\u0004\u0015"+
		"\u00d4\b\u0015\u000b\u0015\f\u0015\u00d5\u0001\u0016\u0001\u0016\u0001"+
		"\u0017\u0001\u0017\u0001\u0017\u0001\u0017\u0001\u0017\u0001\u0017\u0003"+
		"\u0017\u00e0\b\u0017\u0001\u0018\u0004\u0018\u00e3\b\u0018\u000b\u0018"+
		"\f\u0018\u00e4\u0001\u0018\u0000\u0000\u0019\u0000\u0002\u0004\u0006\b"+
		"\n\f\u000e\u0010\u0012\u0014\u0016\u0018\u001a\u001c\u001e \"$&(*,.0\u0000"+
		"\u0001\u0001\u0000\u0018\u0019\u00e7\u00002\u0001\u0000\u0000\u0000\u0002"+
		">\u0001\u0000\u0000\u0000\u0004P\u0001\u0000\u0000\u0000\u0006X\u0001"+
		"\u0000\u0000\u0000\b\\\u0001\u0000\u0000\u0000\ni\u0001\u0000\u0000\u0000"+
		"\f|\u0001\u0000\u0000\u0000\u000e~\u0001\u0000\u0000\u0000\u0010\u0084"+
		"\u0001\u0000\u0000\u0000\u0012\u0087\u0001\u0000\u0000\u0000\u0014\u008a"+
		"\u0001\u0000\u0000\u0000\u0016\u009a\u0001\u0000\u0000\u0000\u0018\u009d"+
		"\u0001\u0000\u0000\u0000\u001a\u00a0\u0001\u0000\u0000\u0000\u001c\u00a7"+
		"\u0001\u0000\u0000\u0000\u001e\u00ad\u0001\u0000\u0000\u0000 \u00b5\u0001"+
		"\u0000\u0000\u0000\"\u00b8\u0001\u0000\u0000\u0000$\u00bb\u0001\u0000"+
		"\u0000\u0000&\u00c2\u0001\u0000\u0000\u0000(\u00ca\u0001\u0000\u0000\u0000"+
		"*\u00d3\u0001\u0000\u0000\u0000,\u00d7\u0001\u0000\u0000\u0000.\u00df"+
		"\u0001\u0000\u0000\u00000\u00e2\u0001\u0000\u0000\u000023\u0003\u0002"+
		"\u0001\u000034\u0003\u0006\u0003\u000048\u0005\u0002\u0000\u000057\u0005"+
		"\u001a\u0000\u000065\u0001\u0000\u0000\u00007:\u0001\u0000\u0000\u0000"+
		"86\u0001\u0000\u0000\u000089\u0001\u0000\u0000\u00009\u0001\u0001\u0000"+
		"\u0000\u0000:8\u0001\u0000\u0000\u0000;=\u0003\u000e\u0007\u0000<;\u0001"+
		"\u0000\u0000\u0000=@\u0001\u0000\u0000\u0000><\u0001\u0000\u0000\u0000"+
		">?\u0001\u0000\u0000\u0000?A\u0001\u0000\u0000\u0000@>\u0001\u0000\u0000"+
		"\u0000AE\u0003\u0010\b\u0000BD\u0003\u000e\u0007\u0000CB\u0001\u0000\u0000"+
		"\u0000DG\u0001\u0000\u0000\u0000EC\u0001\u0000\u0000\u0000EF\u0001\u0000"+
		"\u0000\u0000FH\u0001\u0000\u0000\u0000GE\u0001\u0000\u0000\u0000HI\u0003"+
		"\u0012\t\u0000IJ\u0003\u0004\u0002\u0000JK\u0003\u0016\u000b\u0000K\u0003"+
		"\u0001\u0000\u0000\u0000LO\u0003\u0014\n\u0000MO\u0003\u000e\u0007\u0000"+
		"NL\u0001\u0000\u0000\u0000NM\u0001\u0000\u0000\u0000OR\u0001\u0000\u0000"+
		"\u0000PN\u0001\u0000\u0000\u0000PQ\u0001\u0000\u0000\u0000Q\u0005\u0001"+
		"\u0000\u0000\u0000RP\u0001\u0000\u0000\u0000SW\u0003\b\u0004\u0000TW\u0003"+
		"\n\u0005\u0000UW\u0003\u000e\u0007\u0000VS\u0001\u0000\u0000\u0000VT\u0001"+
		"\u0000\u0000\u0000VU\u0001\u0000\u0000\u0000WZ\u0001\u0000\u0000\u0000"+
		"XV\u0001\u0000\u0000\u0000XY\u0001\u0000\u0000\u0000Y\u0007\u0001\u0000"+
		"\u0000\u0000ZX\u0001\u0000\u0000\u0000[]\u0003\u0018\f\u0000\\[\u0001"+
		"\u0000\u0000\u0000\\]\u0001\u0000\u0000\u0000]e\u0001\u0000\u0000\u0000"+
		"^b\u0003\u001c\u000e\u0000_a\u0003\u001e\u000f\u0000`_\u0001\u0000\u0000"+
		"\u0000ad\u0001\u0000\u0000\u0000b`\u0001\u0000\u0000\u0000bc\u0001\u0000"+
		"\u0000\u0000cf\u0001\u0000\u0000\u0000db\u0001\u0000\u0000\u0000e^\u0001"+
		"\u0000\u0000\u0000fg\u0001\u0000\u0000\u0000ge\u0001\u0000\u0000\u0000"+
		"gh\u0001\u0000\u0000\u0000h\t\u0001\u0000\u0000\u0000iv\u0003\u001a\r"+
		"\u0000jk\u0003\u001c\u000e\u0000ko\u0003 \u0010\u0000ln\u0003\f\u0006"+
		"\u0000ml\u0001\u0000\u0000\u0000nq\u0001\u0000\u0000\u0000om\u0001\u0000"+
		"\u0000\u0000op\u0001\u0000\u0000\u0000pr\u0001\u0000\u0000\u0000qo\u0001"+
		"\u0000\u0000\u0000rs\u0003\"\u0011\u0000su\u0001\u0000\u0000\u0000tj\u0001"+
		"\u0000\u0000\u0000ux\u0001\u0000\u0000\u0000vt\u0001\u0000\u0000\u0000"+
		"vw\u0001\u0000\u0000\u0000w\u000b\u0001\u0000\u0000\u0000xv\u0001\u0000"+
		"\u0000\u0000y}\u0003$\u0012\u0000z}\u0003&\u0013\u0000{}\u0003(\u0014"+
		"\u0000|y\u0001\u0000\u0000\u0000|z\u0001\u0000\u0000\u0000|{\u0001\u0000"+
		"\u0000\u0000}\r\u0001\u0000\u0000\u0000~\u0080\u0005\u0017\u0000\u0000"+
		"\u007f\u0081\u0005\u001d\u0000\u0000\u0080\u007f\u0001\u0000\u0000\u0000"+
		"\u0080\u0081\u0001\u0000\u0000\u0000\u0081\u0082\u0001\u0000\u0000\u0000"+
		"\u0082\u0083\u0005\u001f\u0000\u0000\u0083\u000f\u0001\u0000\u0000\u0000"+
		"\u0084\u0085\u0005\u0001\u0000\u0000\u0085\u0086\u0005\u001a\u0000\u0000"+
		"\u0086\u0011\u0001\u0000\u0000\u0000\u0087\u0088\u0007\u0000\u0000\u0000"+
		"\u0088\u0089\u0005\u001a\u0000\u0000\u0089\u0013\u0001\u0000\u0000\u0000"+
		"\u008a\u008b\u0005\u000f\u0000\u0000\u008b\u0094\u0003*\u0015\u0000\u008c"+
		"\u008d\u0005\u0010\u0000\u0000\u008d\u0095\u0003,\u0016\u0000\u008e\u008f"+
		"\u0005\u0014\u0000\u0000\u008f\u0095\u0003.\u0017\u0000\u0090\u0091\u0005"+
		"\u0016\u0000\u0000\u0091\u0095\u0003.\u0017\u0000\u0092\u0093\u0005\u0015"+
		"\u0000\u0000\u0093\u0095\u0003.\u0017\u0000\u0094\u008c\u0001\u0000\u0000"+
		"\u0000\u0094\u008e\u0001\u0000\u0000\u0000\u0094\u0090\u0001\u0000\u0000"+
		"\u0000\u0094\u0092\u0001\u0000\u0000\u0000\u0095\u0096\u0001\u0000\u0000"+
		"\u0000\u0096\u0094\u0001\u0000\u0000\u0000\u0096\u0097\u0001\u0000\u0000"+
		"\u0000\u0097\u0098\u0001\u0000\u0000\u0000\u0098\u0099\u0005\u001a\u0000"+
		"\u0000\u0099\u0015\u0001\u0000\u0000\u0000\u009a\u009b\u0005\n\u0000\u0000"+
		"\u009b\u009c\u0005\u001a\u0000\u0000\u009c\u0017\u0001\u0000\u0000\u0000"+
		"\u009d\u009e\u0005\u0003\u0000\u0000\u009e\u009f\u0005\u001a\u0000\u0000"+
		"\u009f\u0019\u0001\u0000\u0000\u0000\u00a0\u00a1\u0005\u0007\u0000\u0000"+
		"\u00a1\u00a2\u0005\u0011\u0000\u0000\u00a2\u00a3\u0003.\u0017\u0000\u00a3"+
		"\u00a4\u0005\u0012\u0000\u0000\u00a4\u00a5\u0003.\u0017\u0000\u00a5\u00a6"+
		"\u0005\u001a\u0000\u0000\u00a6\u001b\u0001\u0000\u0000\u0000\u00a7\u00a8"+
		"\u0005\u000f\u0000\u0000\u00a8\u00a9\u0003*\u0015\u0000\u00a9\u00aa\u0005"+
		"\u001a\u0000\u0000\u00aa\u001d\u0001\u0000\u0000\u0000\u00ab\u00ac\u0005"+
		"\u0011\u0000\u0000\u00ac\u00ae\u0003.\u0017\u0000\u00ad\u00ab\u0001\u0000"+
		"\u0000\u0000\u00ad\u00ae\u0001\u0000\u0000\u0000\u00ae\u00b1\u0001\u0000"+
		"\u0000\u0000\u00af\u00b0\u0005\u0012\u0000\u0000\u00b0\u00b2\u0003.\u0017"+
		"\u0000\u00b1\u00af\u0001\u0000\u0000\u0000\u00b1\u00b2\u0001\u0000\u0000"+
		"\u0000\u00b2\u00b3\u0001\u0000\u0000\u0000\u00b3\u00b4\u0005\u001a\u0000"+
		"\u0000\u00b4\u001f\u0001\u0000\u0000\u0000\u00b5\u00b6\u0005\b\u0000\u0000"+
		"\u00b6\u00b7\u0005\u001a\u0000\u0000\u00b7!\u0001\u0000\u0000\u0000\u00b8"+
		"\u00b9\u0005\t\u0000\u0000\u00b9\u00ba\u0005\u001a\u0000\u0000\u00ba#"+
		"\u0001\u0000\u0000\u0000\u00bb\u00bc\u0005\u0004\u0000\u0000\u00bc\u00bd"+
		"\u0005\u0011\u0000\u0000\u00bd\u00be\u0003.\u0017\u0000\u00be\u00bf\u0005"+
		"\u0012\u0000\u0000\u00bf\u00c0\u0003.\u0017\u0000\u00c0\u00c1\u0005\u001a"+
		"\u0000\u0000\u00c1%\u0001\u0000\u0000\u0000\u00c2\u00c3\u0005\u0005\u0000"+
		"\u0000\u00c3\u00c4\u0005\u0011\u0000\u0000\u00c4\u00c5\u0003.\u0017\u0000"+
		"\u00c5\u00c6\u0005\u0012\u0000\u0000\u00c6\u00c7\u0003.\u0017\u0000\u00c7"+
		"\u00c8\u0005\u0013\u0000\u0000\u00c8\u00c9\u0003.\u0017\u0000\u00c9\'"+
		"\u0001\u0000\u0000\u0000\u00ca\u00cb\u0005\u0006\u0000\u0000\u00cb\u00cc"+
		"\u0005\u0011\u0000\u0000\u00cc\u00cd\u0003.\u0017\u0000\u00cd\u00ce\u0005"+
		"\u0012\u0000\u0000\u00ce\u00cf\u0003.\u0017\u0000\u00cf\u00d0\u0005\u0013"+
		"\u0000\u0000\u00d0\u00d1\u0003.\u0017\u0000\u00d1)\u0001\u0000\u0000\u0000"+
		"\u00d2\u00d4\u0005\u001b\u0000\u0000\u00d3\u00d2\u0001\u0000\u0000\u0000"+
		"\u00d4\u00d5\u0001\u0000\u0000\u0000\u00d5\u00d3\u0001\u0000\u0000\u0000"+
		"\u00d5\u00d6\u0001\u0000\u0000\u0000\u00d6+\u0001\u0000\u0000\u0000\u00d7"+
		"\u00d8\u0003.\u0017\u0000\u00d8-\u0001\u0000\u0000\u0000\u00d9\u00e0\u0001"+
		"\u0000\u0000\u0000\u00da\u00db\u00030\u0018\u0000\u00db\u00dc\u0005\r"+
		"\u0000\u0000\u00dc\u00dd\u00030\u0018\u0000\u00dd\u00e0\u0001\u0000\u0000"+
		"\u0000\u00de\u00e0\u00030\u0018\u0000\u00df\u00d9\u0001\u0000\u0000\u0000"+
		"\u00df\u00da\u0001\u0000\u0000\u0000\u00df\u00de\u0001\u0000\u0000\u0000"+
		"\u00e0/\u0001\u0000\u0000\u0000\u00e1\u00e3\u0005\u001b\u0000\u0000\u00e2"+
		"\u00e1\u0001\u0000\u0000\u0000\u00e3\u00e4\u0001\u0000\u0000\u0000\u00e4"+
		"\u00e2\u0001\u0000\u0000\u0000\u00e4\u00e5\u0001\u0000\u0000\u0000\u00e5"+
		"1\u0001\u0000\u0000\u0000\u00158>ENPVX\\bgov|\u0080\u0094\u0096\u00ad"+
		"\u00b1\u00d5\u00df\u00e4";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}