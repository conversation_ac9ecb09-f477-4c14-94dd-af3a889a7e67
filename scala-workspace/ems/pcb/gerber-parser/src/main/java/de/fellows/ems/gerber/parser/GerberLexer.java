// Generated from /home/<USER>/src/epibator/scala-workspace/ems/pcb/gerber-parser/src/main/antlr4/GerberLexer.g4 by ANTLR 4.13.1
package de.fellows.ems.gerber.parser;

import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.ATN;
import org.antlr.v4.runtime.atn.ATNDeserializer;
import org.antlr.v4.runtime.atn.LexerATNSimulator;
import org.antlr.v4.runtime.atn.PredictionContextCache;
import org.antlr.v4.runtime.dfa.DFA;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class GerberLexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.13.1", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		WS=1, EOB=2, PARAM=3, M02=4, G01=5, G02=6, G03=7, G04=8, LN=9, G36=10, 
		G37=11, G74=12, G75=13, FS=14, MO=15, TF=16, IP=17, IN=18, ICAS=19, AD=20, 
		AM=21, AB=22, SR=23, LP=24, TD=25, TO=26, G54=27, G55=28, G70=29, G71=30, 
		G90=31, G91=32, M00=33, M01=34, AS=35, MI=36, IR=37, OF=38, SF=39, N2=40, 
		SYMBOL=41, INT=42, X=43, Y=44, I=45, J=46, A=47, B=48, R=49, C=50, D=51, 
		L=52, T=53, MM=54, MINUS=55, DOT=56, COMMA=57, DOLLAR=58, EQ=59, CHAR=60, 
		COMMENTCONTENT=61, CLOSE=62;
	public static final int
		COMMENTMODE=1;
	public static String[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static String[] modeNames = {
		"DEFAULT_MODE", "COMMENTMODE"
	};

	private static String[] makeRuleNames() {
		return new String[] {
			"WS", "EOB", "PARAM", "M02", "G01", "G02", "G03", "G04", "LN", "G36", 
			"G37", "G74", "G75", "FS", "MO", "TF", "IP", "IN", "ICAS", "AD", "AM", 
			"AB", "SR", "LP", "TD", "TO", "G54", "G55", "G70", "G71", "G90", "G91", 
			"M00", "M01", "AS", "MI", "IR", "OF", "SF", "N2", "SYMBOL", "DIGIT", 
			"INT", "X", "Y", "I", "J", "A", "B", "R", "C", "D", "L", "T", "MM", "MINUS", 
			"DOT", "COMMA", "DOLLAR", "EQ", "CHAR", "COMMENTCONTENT", "CLOSE"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, null, null, "'%'", null, null, null, null, null, "'LN'", "'G36'", 
			"'G37'", "'G74'", "'G75'", "'FS'", "'MO'", "'TF'", "'IP'", "'IN'", "'ICAS'", 
			"'AD'", "'AM'", "'AB'", "'SR'", "'LP'", "'TD'", "'TO'", "'G54'", "'G55'", 
			"'G70'", "'G71'", "'G90'", "'G91'", "'M00'", "'M01'", "'AS'", "'MI'", 
			"'IR'", "'OF'", "'SF'", "'N2'", null, null, "'X'", "'Y'", "'I'", "'J'", 
			"'A'", "'B'", "'R'", "'C'", "'D'", "'L'", "'T'", "'MM'", "'-'", "'.'", 
			"','", "'$'", "'='"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, "WS", "EOB", "PARAM", "M02", "G01", "G02", "G03", "G04", "LN", 
			"G36", "G37", "G74", "G75", "FS", "MO", "TF", "IP", "IN", "ICAS", "AD", 
			"AM", "AB", "SR", "LP", "TD", "TO", "G54", "G55", "G70", "G71", "G90", 
			"G91", "M00", "M01", "AS", "MI", "IR", "OF", "SF", "N2", "SYMBOL", "INT", 
			"X", "Y", "I", "J", "A", "B", "R", "C", "D", "L", "T", "MM", "MINUS", 
			"DOT", "COMMA", "DOLLAR", "EQ", "CHAR", "COMMENTCONTENT", "CLOSE"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public GerberLexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "GerberLexer.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getChannelNames() { return channelNames; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\u0004\u0000>\u014d\u0006\uffff\uffff\u0006\uffff\uffff\u0002\u0000\u0007"+
		"\u0000\u0002\u0001\u0007\u0001\u0002\u0002\u0007\u0002\u0002\u0003\u0007"+
		"\u0003\u0002\u0004\u0007\u0004\u0002\u0005\u0007\u0005\u0002\u0006\u0007"+
		"\u0006\u0002\u0007\u0007\u0007\u0002\b\u0007\b\u0002\t\u0007\t\u0002\n"+
		"\u0007\n\u0002\u000b\u0007\u000b\u0002\f\u0007\f\u0002\r\u0007\r\u0002"+
		"\u000e\u0007\u000e\u0002\u000f\u0007\u000f\u0002\u0010\u0007\u0010\u0002"+
		"\u0011\u0007\u0011\u0002\u0012\u0007\u0012\u0002\u0013\u0007\u0013\u0002"+
		"\u0014\u0007\u0014\u0002\u0015\u0007\u0015\u0002\u0016\u0007\u0016\u0002"+
		"\u0017\u0007\u0017\u0002\u0018\u0007\u0018\u0002\u0019\u0007\u0019\u0002"+
		"\u001a\u0007\u001a\u0002\u001b\u0007\u001b\u0002\u001c\u0007\u001c\u0002"+
		"\u001d\u0007\u001d\u0002\u001e\u0007\u001e\u0002\u001f\u0007\u001f\u0002"+
		" \u0007 \u0002!\u0007!\u0002\"\u0007\"\u0002#\u0007#\u0002$\u0007$\u0002"+
		"%\u0007%\u0002&\u0007&\u0002\'\u0007\'\u0002(\u0007(\u0002)\u0007)\u0002"+
		"*\u0007*\u0002+\u0007+\u0002,\u0007,\u0002-\u0007-\u0002.\u0007.\u0002"+
		"/\u0007/\u00020\u00070\u00021\u00071\u00022\u00072\u00023\u00073\u0002"+
		"4\u00074\u00025\u00075\u00026\u00076\u00027\u00077\u00028\u00078\u0002"+
		"9\u00079\u0002:\u0007:\u0002;\u0007;\u0002<\u0007<\u0002=\u0007=\u0002"+
		">\u0007>\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001"+
		"\u0001\u0001\u0002\u0001\u0002\u0001\u0003\u0001\u0003\u0001\u0003\u0001"+
		"\u0003\u0003\u0003\u008d\b\u0003\u0001\u0004\u0001\u0004\u0001\u0004\u0001"+
		"\u0004\u0003\u0004\u0093\b\u0004\u0001\u0005\u0001\u0005\u0001\u0005\u0001"+
		"\u0005\u0003\u0005\u0099\b\u0005\u0001\u0006\u0001\u0006\u0001\u0006\u0001"+
		"\u0006\u0003\u0006\u009f\b\u0006\u0001\u0007\u0001\u0007\u0001\u0007\u0001"+
		"\u0007\u0003\u0007\u00a5\b\u0007\u0001\u0007\u0001\u0007\u0001\b\u0001"+
		"\b\u0001\b\u0001\t\u0001\t\u0001\t\u0001\t\u0001\n\u0001\n\u0001\n\u0001"+
		"\n\u0001\u000b\u0001\u000b\u0001\u000b\u0001\u000b\u0001\f\u0001\f\u0001"+
		"\f\u0001\f\u0001\r\u0001\r\u0001\r\u0001\u000e\u0001\u000e\u0001\u000e"+
		"\u0001\u000f\u0001\u000f\u0001\u000f\u0001\u0010\u0001\u0010\u0001\u0010"+
		"\u0001\u0011\u0001\u0011\u0001\u0011\u0001\u0012\u0001\u0012\u0001\u0012"+
		"\u0001\u0012\u0001\u0012\u0001\u0013\u0001\u0013\u0001\u0013\u0001\u0014"+
		"\u0001\u0014\u0001\u0014\u0001\u0015\u0001\u0015\u0001\u0015\u0001\u0016"+
		"\u0001\u0016\u0001\u0016\u0001\u0017\u0001\u0017\u0001\u0017\u0001\u0018"+
		"\u0001\u0018\u0001\u0018\u0001\u0019\u0001\u0019\u0001\u0019\u0001\u001a"+
		"\u0001\u001a\u0001\u001a\u0001\u001a\u0001\u001b\u0001\u001b\u0001\u001b"+
		"\u0001\u001b\u0001\u001c\u0001\u001c\u0001\u001c\u0001\u001c\u0001\u001d"+
		"\u0001\u001d\u0001\u001d\u0001\u001d\u0001\u001e\u0001\u001e\u0001\u001e"+
		"\u0001\u001e\u0001\u001f\u0001\u001f\u0001\u001f\u0001\u001f\u0001 \u0001"+
		" \u0001 \u0001 \u0001!\u0001!\u0001!\u0001!\u0001\"\u0001\"\u0001\"\u0001"+
		"#\u0001#\u0001#\u0001$\u0001$\u0001$\u0001%\u0001%\u0001%\u0001&\u0001"+
		"&\u0001&\u0001\'\u0001\'\u0001\'\u0001(\u0001(\u0001)\u0001)\u0001*\u0004"+
		"*\u011c\b*\u000b*\f*\u011d\u0001+\u0001+\u0001,\u0001,\u0001-\u0001-\u0001"+
		".\u0001.\u0001/\u0001/\u00010\u00010\u00011\u00011\u00012\u00012\u0001"+
		"3\u00013\u00014\u00014\u00015\u00015\u00016\u00016\u00016\u00017\u0001"+
		"7\u00018\u00018\u00019\u00019\u0001:\u0001:\u0001;\u0001;\u0001<\u0001"+
		"<\u0001=\u0004=\u0146\b=\u000b=\f=\u0147\u0001>\u0001>\u0001>\u0001>\u0000"+
		"\u0000?\u0002\u0001\u0004\u0002\u0006\u0003\b\u0004\n\u0005\f\u0006\u000e"+
		"\u0007\u0010\b\u0012\t\u0014\n\u0016\u000b\u0018\f\u001a\r\u001c\u000e"+
		"\u001e\u000f \u0010\"\u0011$\u0012&\u0013(\u0014*\u0015,\u0016.\u0017"+
		"0\u00182\u00194\u001a6\u001b8\u001c:\u001d<\u001e>\u001f@ B!D\"F#H$J%"+
		"L&N\'P(R)T\u0000V*X+Z,\\-^.`/b0d1f2h3j4l5n6p7r8t9v:x;z<|=~>\u0002\u0000"+
		"\u0001\u0004\u0003\u0000\t\n\r\r  \u0007\u0000##()++//::@@__\u0002\u0000"+
		"AZaz\u0001\u0000**\u0151\u0000\u0002\u0001\u0000\u0000\u0000\u0000\u0004"+
		"\u0001\u0000\u0000\u0000\u0000\u0006\u0001\u0000\u0000\u0000\u0000\b\u0001"+
		"\u0000\u0000\u0000\u0000\n\u0001\u0000\u0000\u0000\u0000\f\u0001\u0000"+
		"\u0000\u0000\u0000\u000e\u0001\u0000\u0000\u0000\u0000\u0010\u0001\u0000"+
		"\u0000\u0000\u0000\u0012\u0001\u0000\u0000\u0000\u0000\u0014\u0001\u0000"+
		"\u0000\u0000\u0000\u0016\u0001\u0000\u0000\u0000\u0000\u0018\u0001\u0000"+
		"\u0000\u0000\u0000\u001a\u0001\u0000\u0000\u0000\u0000\u001c\u0001\u0000"+
		"\u0000\u0000\u0000\u001e\u0001\u0000\u0000\u0000\u0000 \u0001\u0000\u0000"+
		"\u0000\u0000\"\u0001\u0000\u0000\u0000\u0000$\u0001\u0000\u0000\u0000"+
		"\u0000&\u0001\u0000\u0000\u0000\u0000(\u0001\u0000\u0000\u0000\u0000*"+
		"\u0001\u0000\u0000\u0000\u0000,\u0001\u0000\u0000\u0000\u0000.\u0001\u0000"+
		"\u0000\u0000\u00000\u0001\u0000\u0000\u0000\u00002\u0001\u0000\u0000\u0000"+
		"\u00004\u0001\u0000\u0000\u0000\u00006\u0001\u0000\u0000\u0000\u00008"+
		"\u0001\u0000\u0000\u0000\u0000:\u0001\u0000\u0000\u0000\u0000<\u0001\u0000"+
		"\u0000\u0000\u0000>\u0001\u0000\u0000\u0000\u0000@\u0001\u0000\u0000\u0000"+
		"\u0000B\u0001\u0000\u0000\u0000\u0000D\u0001\u0000\u0000\u0000\u0000F"+
		"\u0001\u0000\u0000\u0000\u0000H\u0001\u0000\u0000\u0000\u0000J\u0001\u0000"+
		"\u0000\u0000\u0000L\u0001\u0000\u0000\u0000\u0000N\u0001\u0000\u0000\u0000"+
		"\u0000P\u0001\u0000\u0000\u0000\u0000R\u0001\u0000\u0000\u0000\u0000V"+
		"\u0001\u0000\u0000\u0000\u0000X\u0001\u0000\u0000\u0000\u0000Z\u0001\u0000"+
		"\u0000\u0000\u0000\\\u0001\u0000\u0000\u0000\u0000^\u0001\u0000\u0000"+
		"\u0000\u0000`\u0001\u0000\u0000\u0000\u0000b\u0001\u0000\u0000\u0000\u0000"+
		"d\u0001\u0000\u0000\u0000\u0000f\u0001\u0000\u0000\u0000\u0000h\u0001"+
		"\u0000\u0000\u0000\u0000j\u0001\u0000\u0000\u0000\u0000l\u0001\u0000\u0000"+
		"\u0000\u0000n\u0001\u0000\u0000\u0000\u0000p\u0001\u0000\u0000\u0000\u0000"+
		"r\u0001\u0000\u0000\u0000\u0000t\u0001\u0000\u0000\u0000\u0000v\u0001"+
		"\u0000\u0000\u0000\u0000x\u0001\u0000\u0000\u0000\u0000z\u0001\u0000\u0000"+
		"\u0000\u0001|\u0001\u0000\u0000\u0000\u0001~\u0001\u0000\u0000\u0000\u0002"+
		"\u0080\u0001\u0000\u0000\u0000\u0004\u0084\u0001\u0000\u0000\u0000\u0006"+
		"\u0086\u0001\u0000\u0000\u0000\b\u0088\u0001\u0000\u0000\u0000\n\u008e"+
		"\u0001\u0000\u0000\u0000\f\u0094\u0001\u0000\u0000\u0000\u000e\u009a\u0001"+
		"\u0000\u0000\u0000\u0010\u00a0\u0001\u0000\u0000\u0000\u0012\u00a8\u0001"+
		"\u0000\u0000\u0000\u0014\u00ab\u0001\u0000\u0000\u0000\u0016\u00af\u0001"+
		"\u0000\u0000\u0000\u0018\u00b3\u0001\u0000\u0000\u0000\u001a\u00b7\u0001"+
		"\u0000\u0000\u0000\u001c\u00bb\u0001\u0000\u0000\u0000\u001e\u00be\u0001"+
		"\u0000\u0000\u0000 \u00c1\u0001\u0000\u0000\u0000\"\u00c4\u0001\u0000"+
		"\u0000\u0000$\u00c7\u0001\u0000\u0000\u0000&\u00ca\u0001\u0000\u0000\u0000"+
		"(\u00cf\u0001\u0000\u0000\u0000*\u00d2\u0001\u0000\u0000\u0000,\u00d5"+
		"\u0001\u0000\u0000\u0000.\u00d8\u0001\u0000\u0000\u00000\u00db\u0001\u0000"+
		"\u0000\u00002\u00de\u0001\u0000\u0000\u00004\u00e1\u0001\u0000\u0000\u0000"+
		"6\u00e4\u0001\u0000\u0000\u00008\u00e8\u0001\u0000\u0000\u0000:\u00ec"+
		"\u0001\u0000\u0000\u0000<\u00f0\u0001\u0000\u0000\u0000>\u00f4\u0001\u0000"+
		"\u0000\u0000@\u00f8\u0001\u0000\u0000\u0000B\u00fc\u0001\u0000\u0000\u0000"+
		"D\u0100\u0001\u0000\u0000\u0000F\u0104\u0001\u0000\u0000\u0000H\u0107"+
		"\u0001\u0000\u0000\u0000J\u010a\u0001\u0000\u0000\u0000L\u010d\u0001\u0000"+
		"\u0000\u0000N\u0110\u0001\u0000\u0000\u0000P\u0113\u0001\u0000\u0000\u0000"+
		"R\u0116\u0001\u0000\u0000\u0000T\u0118\u0001\u0000\u0000\u0000V\u011b"+
		"\u0001\u0000\u0000\u0000X\u011f\u0001\u0000\u0000\u0000Z\u0121\u0001\u0000"+
		"\u0000\u0000\\\u0123\u0001\u0000\u0000\u0000^\u0125\u0001\u0000\u0000"+
		"\u0000`\u0127\u0001\u0000\u0000\u0000b\u0129\u0001\u0000\u0000\u0000d"+
		"\u012b\u0001\u0000\u0000\u0000f\u012d\u0001\u0000\u0000\u0000h\u012f\u0001"+
		"\u0000\u0000\u0000j\u0131\u0001\u0000\u0000\u0000l\u0133\u0001\u0000\u0000"+
		"\u0000n\u0135\u0001\u0000\u0000\u0000p\u0138\u0001\u0000\u0000\u0000r"+
		"\u013a\u0001\u0000\u0000\u0000t\u013c\u0001\u0000\u0000\u0000v\u013e\u0001"+
		"\u0000\u0000\u0000x\u0140\u0001\u0000\u0000\u0000z\u0142\u0001\u0000\u0000"+
		"\u0000|\u0145\u0001\u0000\u0000\u0000~\u0149\u0001\u0000\u0000\u0000\u0080"+
		"\u0081\u0007\u0000\u0000\u0000\u0081\u0082\u0001\u0000\u0000\u0000\u0082"+
		"\u0083\u0006\u0000\u0000\u0000\u0083\u0003\u0001\u0000\u0000\u0000\u0084"+
		"\u0085\u0005*\u0000\u0000\u0085\u0005\u0001\u0000\u0000\u0000\u0086\u0087"+
		"\u0005%\u0000\u0000\u0087\u0007\u0001\u0000\u0000\u0000\u0088\u008c\u0005"+
		"M\u0000\u0000\u0089\u008d\u00052\u0000\u0000\u008a\u008b\u00050\u0000"+
		"\u0000\u008b\u008d\u00052\u0000\u0000\u008c\u0089\u0001\u0000\u0000\u0000"+
		"\u008c\u008a\u0001\u0000\u0000\u0000\u008d\t\u0001\u0000\u0000\u0000\u008e"+
		"\u0092\u0005G\u0000\u0000\u008f\u0093\u00051\u0000\u0000\u0090\u0091\u0005"+
		"0\u0000\u0000\u0091\u0093\u00051\u0000\u0000\u0092\u008f\u0001\u0000\u0000"+
		"\u0000\u0092\u0090\u0001\u0000\u0000\u0000\u0093\u000b\u0001\u0000\u0000"+
		"\u0000\u0094\u0098\u0005G\u0000\u0000\u0095\u0099\u00052\u0000\u0000\u0096"+
		"\u0097\u00050\u0000\u0000\u0097\u0099\u00052\u0000\u0000\u0098\u0095\u0001"+
		"\u0000\u0000\u0000\u0098\u0096\u0001\u0000\u0000\u0000\u0099\r\u0001\u0000"+
		"\u0000\u0000\u009a\u009e\u0005G\u0000\u0000\u009b\u009f\u00053\u0000\u0000"+
		"\u009c\u009d\u00050\u0000\u0000\u009d\u009f\u00053\u0000\u0000\u009e\u009b"+
		"\u0001\u0000\u0000\u0000\u009e\u009c\u0001\u0000\u0000\u0000\u009f\u000f"+
		"\u0001\u0000\u0000\u0000\u00a0\u00a4\u0005G\u0000\u0000\u00a1\u00a5\u0005"+
		"4\u0000\u0000\u00a2\u00a3\u00050\u0000\u0000\u00a3\u00a5\u00054\u0000"+
		"\u0000\u00a4\u00a1\u0001\u0000\u0000\u0000\u00a4\u00a2\u0001\u0000\u0000"+
		"\u0000\u00a5\u00a6\u0001\u0000\u0000\u0000\u00a6\u00a7\u0006\u0007\u0001"+
		"\u0000\u00a7\u0011\u0001\u0000\u0000\u0000\u00a8\u00a9\u0005L\u0000\u0000"+
		"\u00a9\u00aa\u0005N\u0000\u0000\u00aa\u0013\u0001\u0000\u0000\u0000\u00ab"+
		"\u00ac\u0005G\u0000\u0000\u00ac\u00ad\u00053\u0000\u0000\u00ad\u00ae\u0005"+
		"6\u0000\u0000\u00ae\u0015\u0001\u0000\u0000\u0000\u00af\u00b0\u0005G\u0000"+
		"\u0000\u00b0\u00b1\u00053\u0000\u0000\u00b1\u00b2\u00057\u0000\u0000\u00b2"+
		"\u0017\u0001\u0000\u0000\u0000\u00b3\u00b4\u0005G\u0000\u0000\u00b4\u00b5"+
		"\u00057\u0000\u0000\u00b5\u00b6\u00054\u0000\u0000\u00b6\u0019\u0001\u0000"+
		"\u0000\u0000\u00b7\u00b8\u0005G\u0000\u0000\u00b8\u00b9\u00057\u0000\u0000"+
		"\u00b9\u00ba\u00055\u0000\u0000\u00ba\u001b\u0001\u0000\u0000\u0000\u00bb"+
		"\u00bc\u0005F\u0000\u0000\u00bc\u00bd\u0005S\u0000\u0000\u00bd\u001d\u0001"+
		"\u0000\u0000\u0000\u00be\u00bf\u0005M\u0000\u0000\u00bf\u00c0\u0005O\u0000"+
		"\u0000\u00c0\u001f\u0001\u0000\u0000\u0000\u00c1\u00c2\u0005T\u0000\u0000"+
		"\u00c2\u00c3\u0005F\u0000\u0000\u00c3!\u0001\u0000\u0000\u0000\u00c4\u00c5"+
		"\u0005I\u0000\u0000\u00c5\u00c6\u0005P\u0000\u0000\u00c6#\u0001\u0000"+
		"\u0000\u0000\u00c7\u00c8\u0005I\u0000\u0000\u00c8\u00c9\u0005N\u0000\u0000"+
		"\u00c9%\u0001\u0000\u0000\u0000\u00ca\u00cb\u0005I\u0000\u0000\u00cb\u00cc"+
		"\u0005C\u0000\u0000\u00cc\u00cd\u0005A\u0000\u0000\u00cd\u00ce\u0005S"+
		"\u0000\u0000\u00ce\'\u0001\u0000\u0000\u0000\u00cf\u00d0\u0005A\u0000"+
		"\u0000\u00d0\u00d1\u0005D\u0000\u0000\u00d1)\u0001\u0000\u0000\u0000\u00d2"+
		"\u00d3\u0005A\u0000\u0000\u00d3\u00d4\u0005M\u0000\u0000\u00d4+\u0001"+
		"\u0000\u0000\u0000\u00d5\u00d6\u0005A\u0000\u0000\u00d6\u00d7\u0005B\u0000"+
		"\u0000\u00d7-\u0001\u0000\u0000\u0000\u00d8\u00d9\u0005S\u0000\u0000\u00d9"+
		"\u00da\u0005R\u0000\u0000\u00da/\u0001\u0000\u0000\u0000\u00db\u00dc\u0005"+
		"L\u0000\u0000\u00dc\u00dd\u0005P\u0000\u0000\u00dd1\u0001\u0000\u0000"+
		"\u0000\u00de\u00df\u0005T\u0000\u0000\u00df\u00e0\u0005D\u0000\u0000\u00e0"+
		"3\u0001\u0000\u0000\u0000\u00e1\u00e2\u0005T\u0000\u0000\u00e2\u00e3\u0005"+
		"O\u0000\u0000\u00e35\u0001\u0000\u0000\u0000\u00e4\u00e5\u0005G\u0000"+
		"\u0000\u00e5\u00e6\u00055\u0000\u0000\u00e6\u00e7\u00054\u0000\u0000\u00e7"+
		"7\u0001\u0000\u0000\u0000\u00e8\u00e9\u0005G\u0000\u0000\u00e9\u00ea\u0005"+
		"5\u0000\u0000\u00ea\u00eb\u00055\u0000\u0000\u00eb9\u0001\u0000\u0000"+
		"\u0000\u00ec\u00ed\u0005G\u0000\u0000\u00ed\u00ee\u00057\u0000\u0000\u00ee"+
		"\u00ef\u00050\u0000\u0000\u00ef;\u0001\u0000\u0000\u0000\u00f0\u00f1\u0005"+
		"G\u0000\u0000\u00f1\u00f2\u00057\u0000\u0000\u00f2\u00f3\u00051\u0000"+
		"\u0000\u00f3=\u0001\u0000\u0000\u0000\u00f4\u00f5\u0005G\u0000\u0000\u00f5"+
		"\u00f6\u00059\u0000\u0000\u00f6\u00f7\u00050\u0000\u0000\u00f7?\u0001"+
		"\u0000\u0000\u0000\u00f8\u00f9\u0005G\u0000\u0000\u00f9\u00fa\u00059\u0000"+
		"\u0000\u00fa\u00fb\u00051\u0000\u0000\u00fbA\u0001\u0000\u0000\u0000\u00fc"+
		"\u00fd\u0005M\u0000\u0000\u00fd\u00fe\u00050\u0000\u0000\u00fe\u00ff\u0005"+
		"0\u0000\u0000\u00ffC\u0001\u0000\u0000\u0000\u0100\u0101\u0005M\u0000"+
		"\u0000\u0101\u0102\u00050\u0000\u0000\u0102\u0103\u00051\u0000\u0000\u0103"+
		"E\u0001\u0000\u0000\u0000\u0104\u0105\u0005A\u0000\u0000\u0105\u0106\u0005"+
		"S\u0000\u0000\u0106G\u0001\u0000\u0000\u0000\u0107\u0108\u0005M\u0000"+
		"\u0000\u0108\u0109\u0005I\u0000\u0000\u0109I\u0001\u0000\u0000\u0000\u010a"+
		"\u010b\u0005I\u0000\u0000\u010b\u010c\u0005R\u0000\u0000\u010cK\u0001"+
		"\u0000\u0000\u0000\u010d\u010e\u0005O\u0000\u0000\u010e\u010f\u0005F\u0000"+
		"\u0000\u010fM\u0001\u0000\u0000\u0000\u0110\u0111\u0005S\u0000\u0000\u0111"+
		"\u0112\u0005F\u0000\u0000\u0112O\u0001\u0000\u0000\u0000\u0113\u0114\u0005"+
		"N\u0000\u0000\u0114\u0115\u00052\u0000\u0000\u0115Q\u0001\u0000\u0000"+
		"\u0000\u0116\u0117\u0007\u0001\u0000\u0000\u0117S\u0001\u0000\u0000\u0000"+
		"\u0118\u0119\u000209\u0000\u0119U\u0001\u0000\u0000\u0000\u011a\u011c"+
		"\u0003T)\u0000\u011b\u011a\u0001\u0000\u0000\u0000\u011c\u011d\u0001\u0000"+
		"\u0000\u0000\u011d\u011b\u0001\u0000\u0000\u0000\u011d\u011e\u0001\u0000"+
		"\u0000\u0000\u011eW\u0001\u0000\u0000\u0000\u011f\u0120\u0005X\u0000\u0000"+
		"\u0120Y\u0001\u0000\u0000\u0000\u0121\u0122\u0005Y\u0000\u0000\u0122["+
		"\u0001\u0000\u0000\u0000\u0123\u0124\u0005I\u0000\u0000\u0124]\u0001\u0000"+
		"\u0000\u0000\u0125\u0126\u0005J\u0000\u0000\u0126_\u0001\u0000\u0000\u0000"+
		"\u0127\u0128\u0005A\u0000\u0000\u0128a\u0001\u0000\u0000\u0000\u0129\u012a"+
		"\u0005B\u0000\u0000\u012ac\u0001\u0000\u0000\u0000\u012b\u012c\u0005R"+
		"\u0000\u0000\u012ce\u0001\u0000\u0000\u0000\u012d\u012e\u0005C\u0000\u0000"+
		"\u012eg\u0001\u0000\u0000\u0000\u012f\u0130\u0005D\u0000\u0000\u0130i"+
		"\u0001\u0000\u0000\u0000\u0131\u0132\u0005L\u0000\u0000\u0132k\u0001\u0000"+
		"\u0000\u0000\u0133\u0134\u0005T\u0000\u0000\u0134m\u0001\u0000\u0000\u0000"+
		"\u0135\u0136\u0005M\u0000\u0000\u0136\u0137\u0005M\u0000\u0000\u0137o"+
		"\u0001\u0000\u0000\u0000\u0138\u0139\u0005-\u0000\u0000\u0139q\u0001\u0000"+
		"\u0000\u0000\u013a\u013b\u0005.\u0000\u0000\u013bs\u0001\u0000\u0000\u0000"+
		"\u013c\u013d\u0005,\u0000\u0000\u013du\u0001\u0000\u0000\u0000\u013e\u013f"+
		"\u0005$\u0000\u0000\u013fw\u0001\u0000\u0000\u0000\u0140\u0141\u0005="+
		"\u0000\u0000\u0141y\u0001\u0000\u0000\u0000\u0142\u0143\u0007\u0002\u0000"+
		"\u0000\u0143{\u0001\u0000\u0000\u0000\u0144\u0146\b\u0003\u0000\u0000"+
		"\u0145\u0144\u0001\u0000\u0000\u0000\u0146\u0147\u0001\u0000\u0000\u0000"+
		"\u0147\u0145\u0001\u0000\u0000\u0000\u0147\u0148\u0001\u0000\u0000\u0000"+
		"\u0148}\u0001\u0000\u0000\u0000\u0149\u014a\u0005*\u0000\u0000\u014a\u014b"+
		"\u0001\u0000\u0000\u0000\u014b\u014c\u0006>\u0002\u0000\u014c\u007f\u0001"+
		"\u0000\u0000\u0000\t\u0000\u0001\u008c\u0092\u0098\u009e\u00a4\u011d\u0147"+
		"\u0003\u0006\u0000\u0000\u0005\u0001\u0000\u0004\u0000\u0000";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}