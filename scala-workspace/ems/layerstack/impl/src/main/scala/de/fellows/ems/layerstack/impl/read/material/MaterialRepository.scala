package de.fellows.ems.layerstack.impl.read.material

import akka.Done
import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row, SimpleStatement}
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.ems.layerstack.api.{Material, MaterialProperties}
import de.fellows.ems.layerstack.impl.entities.material.{MaterialChanged, MaterialCreated, MaterialDeleted, MaterialEvent}
import de.fellows.ems.layerstack.impl.utils.RequestFilter
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta._

import scala.concurrent.{ExecutionContext, Future}

class MaterialRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends StackrateLogging {
  PCBCodecHelper.registerPCBCodecs(session)

  def countBy(team: String, property: String): Future[Seq[(String, BigDecimal)]] =
    if (MaterialProperties.ALL_PROPERTIES.map(_.toLowerCase()).contains(property.toLowerCase())) {
      session.selectAll(s"SELECT * FROM property${property.toLowerCase()} WHERE team = ? ", team).map(
        _.map(_.getString("indexvalue")).groupBy(identity).map { (x: (String, Seq[String])) =>
          (Option(x._1).getOrElse(""), BigDecimal(x._2.length))
        }.toSeq
      )
    } else {
      Future.successful(Seq())
    }

  def getMaterial(team: String, id: String): Future[Option[Material]] =
    session.selectOne("SELECT * FROM materialsById WHERE team = ? AND id = ?", team, id).map(_.map(toMaterial(false)))

  def getMaterials(team: String, ids: Iterable[String]): Future[Seq[Material]] =
    if (ids.isEmpty) {
      Future.successful(Seq())
    } else {
      val l = new java.util.LinkedList[String]()
      ids.foreach(l.add)
      session.selectAll("SELECT * FROM materialsById WHERE team = ? AND id IN ?", team, l).map(_.map(toMaterial(false)))
    }

  //    Future.sequence(ids.map(id => getMaterial(team, id)))

  def getAllMaterials(
      team: String,
      page: Int,
      pagesize: Int,
      filter: Option[RequestFilter],
      flat: Option[Boolean]
  ): Future[Seq[Material]] = {

    val matFilter: Material => Boolean = material =>
      filter match {
        case Some(filter) => MaterialRepository.materialFilter(material, filter)
        case None         => true
      }

    getMaterials(team, page, pagesize, flat, matFilter).runWith(Sink.seq)
  }

  private def getMaterials(
      team: String,
      page: Int,
      pagesize: Int,
      flat: Option[Boolean],
      filter: (Material => Boolean)
  ) = {

    val statement =
      flat match {
        case Some(true) =>
          new SimpleStatement(s"SELECT team,id,name,materialType FROM materialsById WHERE team = ? ", team)
        case _ => new SimpleStatement(s"SELECT * FROM materialsById WHERE team = ?", team)
      }

    statement.setFetchSize(pagesize)

    val source = session.select(
      statement
    )

    source.map(toMaterial(flat.getOrElse(false))).drop(page * pagesize).filter(filter).take(pagesize)
  }

  def toMaterial(flat: Boolean)(r: Row): Material =
    Material(
      team = Option(r.getString("team")),
      id = Option(r.getString("id")),
      name = Option(r.getString("name")),
      meta =
        if (flat) {
          None
        } else {
          try
            Option(r.get("meta", classOf[MetaInfo])).map(_.toLowerCase)
          catch {
            case e: IllegalArgumentException => None
          }
        },
      materialType = Option(r.getString("materialType"))
    )

}

object MaterialRepository {
  def materialFilter(material: Material, filter: Option[RequestFilter]): Boolean =
    filter match {
      case Some(f) => materialFilter(material, f)
      case None    => true
    }

  def materialFilter(material: Material, filter: RequestFilter): Boolean =
    filter.allows(material.meta, Seq("name", "materialtype")) &&
      filter.elements.find(_._1.toLowerCase == "name").forall { e =>
        filter.allows(e, StringProperty("name", material.name.get))
      } &&
      filter.elements.find(_._1.toLowerCase == "materialtype").forall { e =>
        material.materialType match {
          case Some(matType) => filter.allows(e, StringProperty("materialType", matType))
          case None          => false
        }
      }

}

private[impl] class MaterialEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[MaterialEvent] with StackrateLogging {
  PCBCodecHelper.registerPCBCodecs(session)

  var updateMaterialByIDStmt: PreparedStatement = _
  var deleteMaterialByIDStmt: PreparedStatement = _

  // language=SQL
  def createTables(): Future[Done] =
    for {
      _ <- PCBCodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS materialsById (
          |   team text,
          |   id text,
          |   name text,
          |   meta frozen<metainfotype>,
          |   materialType text,
          |   PRIMARY KEY(team, id)
          | )
          |""".stripMargin
      )

    } yield Done

  def prepareStatements(): Future[Done] =
    // language=SQL
    for {
      updateMaterialByID <- session
        .prepare(
          "UPDATE materialsById SET name =:name, meta = :meta, materialType = :materialType WHERE team = :team AND id = :id"
        )
      deleteMaterialByID <- session
        .prepare(
          "DELETE FROM materialsById WHERE team = :team AND id = :id"
        )

    } yield {
      PCBCodecHelper.registerPCBCodecs(session)

      this.updateMaterialByIDStmt = updateMaterialByID
      this.deleteMaterialByIDStmt = deleteMaterialByID
      Done
    }

  def changeMaterial(event: MaterialChanged): Future[Seq[BoundStatement]] = {
    logger.info(s"change material ${event.updated.id}")
    Future.successful(
      this.bindAllMaterialStatements(event.updated, Some(event.old))
    )
  }

  def removeMaterial(event: MaterialDeleted): Future[Seq[BoundStatement]] = {
    logger.info(s"remove material ${event.deleted.id}")
    Future.successful(
      List(
        this.deleteMaterialByIDStmt.bind()
          .setString("team", event.deleted.team.orNull)
          .setString("id", event.deleted.id.orNull)
      )
    )
  }

  def addMaterial(event: MaterialCreated): Future[Seq[BoundStatement]] = {
    val material = event.mat
    logger.info(s"add material ${material.id}")
    Future.successful(
      this.bindAllMaterialStatements(event.mat, None)
    )
  }

  def bindAllMaterialStatements(material: Material, old: Option[Material]): List[BoundStatement] =
    List(
      this.bindMaterialStatement(this.updateMaterialByIDStmt, material)
    )

  def bindMaterialStatement(updateMaterialByIDStmt: PreparedStatement, material: Material): BoundStatement =
    updateMaterialByIDStmt.bind()
      .setString("name", material.name.orNull)
      .set("meta", material.meta.orNull, classOf[MetaInfo])
      .setString("materialType", material.materialType.map(_.toLowerCase).orNull)
      .setString("team", material.team.orNull)
      .setString("id", material.id.orNull)

  override def buildHandler(): ReadSideHandler[MaterialEvent] = readSide.builder[MaterialEvent]("materialrepo-v1.1")
    .setGlobalPrepare(createTables _)
    .setPrepare(_ => prepareStatements())
    .setEventHandler[MaterialChanged](e => changeMaterial(e.event))
    .setEventHandler[MaterialDeleted](e => removeMaterial(e.event))
    .setEventHandler[MaterialCreated](e => addMaterial(e.event))
    .build()

  override def aggregateTags: Set[AggregateEventTag[MaterialEvent]] = MaterialEvent.Tag.allTags
}
