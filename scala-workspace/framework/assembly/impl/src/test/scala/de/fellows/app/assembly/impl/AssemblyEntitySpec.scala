package de.fellows.app.assembly.impl

import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.playjson.JsonSerializerRegistry
import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assembly.impl.entities._
import de.fellows.app.assemby.api.enums._
import de.fellows.ems.pcb.api.PCBFeatures
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.communication.{ServiceDefinition, ServiceException}
import de.fellows.utils.internal._
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.nio.file.Files
import java.time.Instant
import java.util.UUID
import akka.Done
import de.fellows.app.assembly.commons.ProjectType
import de.fellows.app.assemby.api.IndividualAssemblyLifecycleStage
import de.fellows.app.assemby.api.AssemblyLifecycleStageName
import java.time.temporal.ChronoUnit
import org.threeten.bp.temporal

import scala.concurrent.duration._
import org.scalatest.OptionValues

class AssemblyEntitySpec extends AnyWordSpec with BeforeAndAfterAll with Matchers with OptionValues {

  implicit val config: Config = ConfigFactory.load()

  type TestDriver = PersistentEntityTestDriver[AssemblyCommand, AssemblyEvent, Option[Assembly]]

  private val system: ActorSystem =
    ActorSystem("AssemblyEntitySpec", JsonSerializerRegistry.actorSystemSetupFor(AssemblyServiceSerializerRegistry))

  implicit val service: ServiceDefinition = ServiceDefinition("assembly")

  val tcmd         = TimelineCommand.system
  val assemblyName = "testassembly"
  val assemblyDesc = "yo"
  val fileIDs      = Seq(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID())
  val eid          = UUID.randomUUID()
  val gid          = "testassembly"

  val fakeOwner = UUID.randomUUID()

  private val team         = "team"
  private val now: Instant = Instant.now
  val baseref = BaseAssemblyReference(
    eid = eid,
    gid = gid,
    team = team,
    creator = fakeOwner.toString,
    created = now,
    customer = None,
    orderId = None,
    assignee = None,
    itemNo = None,
    name = assemblyName,
    description = Some(assemblyDesc),
    project = None,
    status = AssemblyStatus.INITIAL_STATUS,
    uiStatus = UIStatus.DEFAULT,
    preview = None,
    features = Seq(PCBFeatures.PCB_FEATURE),
    currentVersion = Some(UUID.randomUUID()),
    mail = None,
    template = None,
    externalReference = None
  )

  val fakepath  = FilePath("/tmp/", "", "", "", None, "unittest")
  val fakepath2 = FilePath("/tmp/", "", "", "", None, "unittest2")
  val fakepath3 = FilePath("/tmp/", "", "", "", None, "unittest3")

  Files.write(fakepath.toJavaPath, "wow".getBytes())
  Files.write(fakepath2.toJavaPath, "wow2".getBytes())
  Files.write(fakepath3.toJavaPath, "wow3".getBytes())

  val expected = Assembly(
    eid = eid,
    gid = gid,
    name = assemblyName,
    creator = fakeOwner.toString,
    created = now,
    team = team,
    information = AssemblyInformation(
      customer = None,
      orderId = None,
      assignee = None,
      itemNo = None,
      description = Some(assemblyDesc),
      uiStatus = UIStatus.DEFAULT,
      project = None
    ),
    status = AssemblyStatus.INITIAL_STATUS,
    versions = Seq(),
    currentVersion = None,
    features = Seq(PCBFeatures.PCB_FEATURE),
    preview = None
  )

  private def withDriver(block: TestDriver => Unit): Unit = {
    val driver = new PersistentEntityTestDriver(system, new AssemblyEntity, assemblyName)
    block(driver)
  }

  "AssemblyNames" should {
    "replace whitespace" in {
      AssemblyServiceImpl.replaceName("Test Name") shouldBe "TestName"
      AssemblyServiceImpl.replaceName("Test name") shouldBe "TestName"
      AssemblyServiceImpl.replaceName("test name") shouldBe "TestName"
      AssemblyServiceImpl.replaceName("test   name") shouldBe "TestName"
      AssemblyServiceImpl.replaceName("test  \r name\n") shouldBe "TestName"
    }

    "replace non-ascii" in {
      AssemblyServiceImpl.replaceName("\u00AETest   \u2665Name") shouldBe "TestName"
    }
  }

  private def initializeAssembly(driver: TestDriver) = {
    val assembly = CreateAssembly(team, baseref, None, tcmd)
    driver.run(assembly)
    val createdOutcome = driver.run(
      CreateNewVersion(
        team = team,
        assemblyID = baseref.eid,
        versionID = baseref.currentVersion.get,
        keepFiles = false,
        template = None,
        timeline = tcmd,
        withoutFiles = None
      )
    )
    (assembly, createdOutcome)
  }

  "Assembly" should {
    def approve(driver: TestDriver, manual: Boolean = false) =
      driver.run(ApproveFileMatching(team, manualAction = manual, tcmd))
    def unlock(driver: TestDriver, manual: Boolean = false) =
      driver.run(UnlockFileMatching(team, manualAction = manual, tcmd))

    "be locked after successful ApproveFileMatching command" in withDriver { driver =>
      val (_, createdOutcome) = initializeAssembly(driver)
      createdOutcome.state.map(_.locked) shouldBe Some(false)

      val approvedOutcome = approve(driver)
      approvedOutcome.state.map(_.locked) shouldBe Some(true)
    }
    "be unlocked after successful UnlockFileMatching command" in withDriver { driver =>
      initializeAssembly(driver)

      val approvedOutcome = approve(driver)
      approvedOutcome.state.map(_.locked) shouldBe Some(true)

      val unlockedOutcome = unlock(driver)
      unlockedOutcome.state.map(_.locked) shouldBe Some(false)
    }
    "be manually unlocked after UnlockFileMatching command with manualAction = true" in withDriver { driver =>
      initializeAssembly(driver)
      val approvedOutcome = approve(driver)
      approvedOutcome.state.map(_.locked) shouldBe Some(true)
      approvedOutcome.state.map(_.manuallyUnlocked) shouldBe Some(false)

      val unlockedOutcome = unlock(driver, manual = true)
      unlockedOutcome.state.map(_.locked) shouldBe Some(false)
      unlockedOutcome.state.map(_.manuallyUnlocked) shouldBe Some(true)
    }
    "be manually locked after ApproveFileMatching command with manualAction = true" in withDriver { driver =>
      initializeAssembly(driver)
      approve(driver)
      unlock(driver, manual = true)

      val approvedOutcome = approve(driver, manual = true)
      approvedOutcome.state.map(_.locked) shouldBe Some(true)
      approvedOutcome.state.map(_.manuallyUnlocked) shouldBe Some(false)
    }
  }
  "Assembly" should {
    "allow creation" in withDriver { driver =>
      val ass     = CreateAssembly(team, baseref, None, tcmd)
      val outcome = driver.run(ass)

      outcome.state shouldBe
        Some(
          expected.copy(features = Seq(PCBFeatures.PCB_FEATURE))
        )
    }

    "lifecycle" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      var outcome = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = outcome.replies.head.asInstanceOf[Version]

      val fileName  = "fileName1"
      val fileName2 = "fileName2"
      driver.run(AddFile(
        team,
        fileIDs(0),
        fileName,
        fakepath,
        FileType.UNKNOWN,
        Seq(),
        tcmd,
        keepDuplicates = false
      ))

      driver.run(AddFile(
        team,
        fileIDs(1),
        fileName2,
        fakepath,
        FileType.UNKNOWN,
        Seq(),
        tcmd,
        keepDuplicates = false
      ))

      val now = System.currentTimeMillis()

      val newLifecycle = FileLifecycleStage(FileLifecycleStageName.Render, LifecycleStageStatus.emptyProgress, None)

      outcome = driver.run(UpdateFileLifecycle(team, baseref.eid, version.id, fileName, newLifecycle, Some(now), tcmd))
      outcome = driver.run(UpdateFileLifecycle(team, baseref.eid, version.id, fileName2, newLifecycle, Some(now), tcmd))

      val now2 = System.currentTimeMillis()
      val newLifecycle2 =
        FileLifecycleStage(FileLifecycleStageName.FileAnalysis, LifecycleStageStatus.emptySuccess, None)

      outcome =
        driver.run(UpdateFileLifecycle(team, baseref.eid, version.id, fileName, newLifecycle2, Some(now2), tcmd))
      val now3 = now2 + 1000
      outcome =
        driver.run(UpdateFileLifecycle(team, baseref.eid, version.id, fileName2, newLifecycle2, Some(now3), tcmd))

      outcome = driver.run(GetAssembly(team, baseref.eid))

      val lcs = outcome.state.flatMap(a =>
        a.currentVersion.flatMap(_.files.find(_.name == fileName).flatMap(_.lifecycles))
      ).getOrElse(Seq())

      val history = Some(Seq(
        HistoricLifecycleState(
          StageStatusName.Success,
          now2,
          None
        )
      ))

      val actual = lcs.sortBy(_.name.value)
      val expected = Seq(
        newLifecycle.copy(history =
          Some(Seq(
            HistoricLifecycleState(
              StageStatusName.Progress,
              now,
              None
            )
          ))
        ),
        newLifecycle2.copy(history = history)
      ).sortBy(_.name.value)

      actual shouldBe expected
    }

    "allow nameChange" in withDriver { driver =>
      val newName = "this is a new name"
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val outcome = driver.run(SetName(eid, team, newName, tcmd))

      outcome.state shouldBe Some(expected.copy(name = newName))
    }

    "allow initial version" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val outcome = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )

      outcome.replies.length shouldBe 1
      outcome.replies.head shouldBe a[Version]
      val version = outcome.replies.head.asInstanceOf[Version]
      outcome.state shouldBe
        Some(expected.copy(currentVersion = Some(version)))

    }

    "forbid multiple open versions" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      var outcome = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )

      outcome.replies.length shouldBe 1
      outcome.replies.head shouldBe a[Version]
      val version = outcome.replies.head.asInstanceOf[Version]

      outcome = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )

      outcome.replies.head shouldBe a[ServiceException]
      outcome.replies.head shouldBe new ServiceException(AssemblyHasOpenVersion)

      outcome.state shouldBe Some(expected.copy(currentVersion = Some(version)))
    }

    "allow creation and releasing" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = newV.replies.head.asInstanceOf[Version]

      val vName    = "yo"
      val outcome  = driver.run(ReleaseAssembly(team, eid, vName, tcmd))
      val released = outcome.replies.head.asInstanceOf[Version]

      outcome.state shouldBe
        Some(expected.copy(versions = Seq(version.copy(name = Some(vName), released = released.released))))
    }

    "forbid rereleasing" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = newV.replies.head.asInstanceOf[Version]

      val vName = "yo"

      var outcome  = driver.run(ReleaseAssembly(team, eid, vName, tcmd))
      val released = outcome.replies.head.asInstanceOf[Version]

      outcome = driver.run(ReleaseAssembly(team, eid, vName, tcmd))

      outcome.replies.head shouldBe a[ServiceException]
      outcome.replies.head shouldBe new ServiceException(NoOpenVersionFound)

      outcome.state shouldBe
        Some(expected.copy(versions = Seq(version.copy(name = Some(vName), released = released.released))))
    }

    "allow setting of the description" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))

      val newDesc = "new desc"
      val outcome = driver.run(SetDescription(team, eid, Some(newDesc), tcmd))
      outcome.state shouldBe
        Some(expected.copy(information = expected.information.copy(description = Some(newDesc))))
    }

    "allow removing the description" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))

      val outcome = driver.run(SetDescription(team, eid, None, tcmd))
      outcome.state shouldBe
        Some(expected.copy(information = expected.information.copy(description = None)))
    }

    "forbid adding of files without version" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))

      val outcome = driver.run(AddFile(
        team,
        fileIDs(0),
        "fileName",
        fakepath,
        FileType.UNKNOWN,
        Seq(),
        tcmd,
        keepDuplicates = false
      ))

      outcome.replies.head shouldBe new ServiceException(NoOpenVersionFound)

      outcome.state shouldBe
        Some(expected)
    }

    "do nothing when adding the same file twice" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = newV.replies.head.asInstanceOf[Version]

      val file1 = ("fileName", fakepath)
      val outcome = driver.run(
        AddFile(
          team,
          fileIDs(0),
          file1._1,
          file1._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val lc       = outcome.events.head.asInstanceOf[VersionLifecycleUpdated].lc.value
      val deadline = lc.status.deadline.value
      val f1time =
        outcome.events.collectFirst { case e: FilesAdded => e.file.head.created }.getOrElse(throw new RuntimeException(
          "No FilesAdded event found"
        ))
      val outcome2 = driver.run(AddFile(
        team,
        fileIDs(0),
        file1._1,
        file1._2,
        FileType.UNKNOWN,
        Seq(),
        tcmd,
        keepDuplicates = false
      ))

      outcome2.replies.head shouldBe Done

      outcome2.state shouldBe
        Some(
          expected.copy(
            currentVersion = Some(
              version.copy(
                files = version.files ++ Seq(
                  File(fileIDs(0), file1._1, file1._2, FileType.UNKNOWN, Seq(), f1time, None, hash = None)
                ),
                projectType = Some(ProjectType.NoFiles),
                lifecycles = Some(List(IndividualAssemblyLifecycleStage(
                  AssemblyLifecycleStageName.Files,
                  LifecycleStageStatus(
                    StageStatusName.Progress,
                    List(),
                    None,
                    None,
                    Some(deadline)
                  ),
                  lc.history
                )))
              )
            )
          )
        )
    }

    "allow adding of files with open version" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = newV.replies.head.asInstanceOf[Version]

      val file1 = ("fileName", fakepath)
      val file2 = ("fileName2", fakepath2)

      val o1 = driver.run(
        AddFile(
          team,
          fileIDs(0),
          file1._1,
          file1._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val f1time = o1.events
        .collectFirst { case e: FilesAdded => e.file.head.created }
        .getOrElse(throw new RuntimeException("No FilesAdded event found"))

      val outcome = driver.run(
        AddFile(
          team,
          fileIDs(1),
          file2._1,
          file2._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val f2time = outcome.events
        .collectFirst { case e: FilesAdded => e.file.head.created }
        .getOrElse(throw new RuntimeException("No FilesAdded event found"))
      val lc2 = outcome.events
        .collectFirst { case e: VersionLifecycleUpdated => e.lc.value }
        .getOrElse(throw new RuntimeException("No VersionLifecycleUpdated event found"))
      val deadline = lc2.status.deadline.value

      outcome.state shouldBe
        Some(expected.copy(currentVersion =
          Some(
            version.copy(
              projectType = Some(ProjectType.NoFiles),
              files = version.files ++ Seq(
                File(fileIDs(0), file1._1, file1._2, FileType.UNKNOWN, Seq(), f1time, None, hash = None),
                File(fileIDs(1), file2._1, file2._2, FileType.UNKNOWN, Seq(), f2time, None, hash = None)
              ),
              lifecycles = Some(List(IndividualAssemblyLifecycleStage(
                AssemblyLifecycleStageName.Files,
                LifecycleStageStatus(
                  StageStatusName.Progress,
                  List(),
                  None,
                  None,
                  Some(deadline)
                ),
                lc2.history
              )))
            )
          )
        ))

    }

    "allow adding and changing of hints" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      var version = newV.replies.head.asInstanceOf[Version]

      val hint    = Hint(UUID.randomUUID(), "pcb", "A Hint", Some("A nice Hint"), HintOpen, LevelWarning)
      val hint2   = Hint(UUID.randomUUID(), "pcb", "A second Hint", Some("A nice second Hint"), HintOpen, LevelWarning)
      var outcome = driver.run(AddHint(team, hint))
      outcome = driver.run(AddHint(team, hint2))

      version = version.copy(hints = Seq(hint, hint2))
      outcome.state shouldBe
        Some(expected.copy(currentVersion = Some(version)))

      outcome = driver.run(ChangeHintState(team, hint.id, HintIgnored))

      version = version.copy(hints = Seq(hint.copy(status = HintIgnored), hint2))
      outcome.state shouldBe
        Some(expected.copy(currentVersion = Some(version)))

    }

    "forbid changing of illegal hints" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      var version = newV.replies.head.asInstanceOf[Version]

      val hint    = Hint(UUID.randomUUID(), "pcb", "A Hint", Some("A nice Hint"), HintOpen, LevelWarning)
      val hint2   = Hint(UUID.randomUUID(), "pcb", "A second Hint", Some("A nice second Hint"), HintOpen, LevelWarning)
      var outcome = driver.run(AddHint(team, hint))
      outcome = driver.run(AddHint(team, hint2))

      version = version.copy(hints = Seq(hint, hint2))
      outcome.state shouldBe
        Some(expected.copy(currentVersion = Some(version)))

      outcome = driver.run(ChangeHintState(team, UUID.randomUUID(), HintIgnored))

      version = version.copy(hints = Seq(hint, hint2))
      outcome.state shouldBe
        Some(expected.copy(currentVersion = Some(version)))

      outcome.replies.head shouldBe new ServiceException(HintDoesNotExist)

    }

    "forbid changing of hints of closed versions" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      var version = newV.replies.head.asInstanceOf[Version]

      val hint    = Hint(UUID.randomUUID(), "pcb", "A Hint", Some("A nice Hint"), HintOpen, LevelWarning)
      val hint2   = Hint(UUID.randomUUID(), "pcb", "A second Hint", Some("A nice second Hint"), HintOpen, LevelWarning)
      var outcome = driver.run(AddHint(team, hint))
      outcome = driver.run(AddHint(team, hint2))
      val versionName = "v1"
      outcome = driver.run(ReleaseAssembly(team, eid, versionName, tcmd))

      val rel = outcome.replies.head.asInstanceOf[Version].released

      version = version.copy(hints = Seq(hint, hint2), name = Some(versionName), released = rel)
      val exp = Some(expected.copy(currentVersion = None, versions = expected.versions ++ Seq(version)))

      outcome.state shouldBe exp

      // Set hint state while no open version exists
      outcome = driver.run(ChangeHintState(team, hint.id, HintIgnored))
      outcome.replies.head shouldBe new ServiceException(NoOpenVersionFound)
      outcome.state shouldBe exp

      // Set hint state of an old version while a new open version exists
      outcome = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val newVersion = outcome.replies.head.asInstanceOf[Version]
      outcome = driver.run(ChangeHintState(team, hint.id, HintIgnored))

      outcome.replies.head shouldBe new ServiceException(HintDoesNotExist)
      outcome.state shouldBe exp.map(v => v.copy(currentVersion = Some(newVersion)))

    }

    "allow setting the filetype" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = newV.replies.head.asInstanceOf[Version]

      val file1 = ("fileName", fakepath)
      val file2 = ("fileName2", fakepath2)

      val o1 = driver.run(
        AddFile(
          team,
          fileIDs(0),
          file1._1,
          file1._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val lc1 = o1.events
        .collectFirst { case e: VersionLifecycleUpdated => e.lc.value }
        .getOrElse(throw new RuntimeException("No VersionLifecycleUpdated event found"))
      val f1time =
        o1.events
          .collectFirst { case e: FilesAdded => e.file.head.created }
          .getOrElse(throw new RuntimeException("No FilesAdded event found"))

      val o2 = driver.run(
        AddFile(
          team,
          fileIDs(1),
          file2._1,
          file2._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val lc2 = o2.events
        .collectFirst { case e: VersionLifecycleUpdated => e.lc.value }
        .getOrElse(throw new RuntimeException("No VersionLifecycleUpdated event found"))
      val f2time =
        o2.events
          .collectFirst { case e: FilesAdded => e.file.head.created }
          .getOrElse(throw new RuntimeException("No FilesAdded event found"))

      val newFileType = FileType("testservice", None, "testtype", false, None)
      val outcome     = driver.run(UpdateFileType(team, file1._1, Some(newFileType), None))
      val deadline    = lc2.status.deadline.value

      outcome.state shouldBe
        Some(expected.copy(currentVersion =
          Some(
            version.copy(
              projectType = Some(ProjectType.NoFiles),
              files = version.files ++ Seq(
                File(fileIDs(0), file1._1, file1._2, newFileType, Seq(), f1time, None, hash = None),
                File(fileIDs(1), file2._1, file2._2, FileType.UNKNOWN, Seq(), f2time, None, hash = None)
              ),
              lifecycles = Some(List(IndividualAssemblyLifecycleStage(
                AssemblyLifecycleStageName.Files,
                LifecycleStageStatus(
                  StageStatusName.Progress,
                  List(),
                  None,
                  None,
                  Some(deadline)
                ),
                lc1.history
              )))
            )
          )
        ))
    }

    "allow detecting and setting the filetype" in withDriver { driver =>
      driver.run(CreateAssembly(team, baseref, None, tcmd))
      val newV = driver.run(
        CreateNewVersion(
          team = team,
          assemblyID = baseref.eid,
          versionID = baseref.currentVersion.get,
          keepFiles = false,
          template = None,
          timeline = tcmd,
          withoutFiles = None
        )
      )
      val version = newV.replies.head.asInstanceOf[Version]

      val file1 = ("fileName", fakepath)
      val file2 = ("fileName2", fakepath2)

      val o1 = driver.run(
        AddFile(
          team,
          fileIDs(0),
          file1._1,
          file1._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val lc1 = o1.events
        .collectFirst { case e: VersionLifecycleUpdated => e.lc.value }
        .getOrElse(throw new RuntimeException("No VersionLifecycleUpdated event found"))
      val f1time =
        o1.events
          .collectFirst { case e: FilesAdded => e.file.head.created }
          .getOrElse(throw new RuntimeException("No FilesAdded event found"))

      val o2 = driver.run(
        AddFile(
          team,
          fileIDs(1),
          file2._1,
          file2._2,
          FileType.UNKNOWN,
          Seq(),
          tcmd,
          keepDuplicates = false
        )
      )
      val lc2 = o2.events
        .collectFirst { case e: VersionLifecycleUpdated => e.lc.value }
        .getOrElse(throw new RuntimeException("No VersionLifecycleUpdated event found"))
      val f2time =
        o2.events
          .collectFirst { case e: FilesAdded => e.file.head.created }
          .getOrElse(throw new RuntimeException("No FilesAdded event found"))

      val newFileType  = FileType("testservice", None, "testtype", false, None)
      val newFileType2 = FileType("testservice2", None, "testtype2", false, None)

      val outcome = driver.run(UpdateFileType(team, file1._1, Some(newFileType), Some(Seq(newFileType2))))

      val deadline = lc2.status.deadline.value

      outcome.state shouldBe
        Some(expected.copy(currentVersion =
          Some(
            version.copy(
              projectType = Some(ProjectType.NoFiles),
              files = version.files ++ Seq(
                File(
                  fileIDs(0),
                  file1._1,
                  file1._2,
                  newFileType,
                  Seq(newFileType2),
                  f1time,
                  None,
                  hash = None
                ),
                File(fileIDs(1), file2._1, file2._2, FileType.UNKNOWN, Seq(), f2time, None, hash = None)
              ),
              lifecycles = Some(List(IndividualAssemblyLifecycleStage(
                AssemblyLifecycleStageName.Files,
                LifecycleStageStatus(
                  StageStatusName.Progress,
                  List(),
                  None,
                  None,
                  Some(deadline)
                ),
                lc1.history
              )))
            )
          )
        ))
    }
  }
}
