package de.fellows.microservices.pcb.model.pcb.props

object HasLaminationCycles {
  val name: String  = "hasLaminationCycles"
  val label: String = "pcb.board.advanced.hasLaminationCycles"

  val yes: HasLaminationCycles = HasLaminationCycles(true)
  val no: HasLaminationCycles  = HasLaminationCycles(false)

  def apply(value: Option[Boolean]): HasLaminationCycles =
    value.fold(no)(HasLaminationCycles(_))
}

final case class HasLaminationCycles(override val value: Boolean) extends YesNoPCBProperty {
  val fieldName: String = HasLaminationCycles.name
  val label: String     = HasLaminationCycles.label
} 