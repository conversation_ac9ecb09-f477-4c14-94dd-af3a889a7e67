package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import de.fellows.luminovo.panel.PanelDetails
import de.fellows.microservices.pcb.{PanelError, PanelErrorKind}
import de.fellows.microservices.pcb.model.Millimeters
import io.opentelemetry.api.trace.{Span, StatusCode}
import play.api.Logging
import zio.prelude.Validation
import zio.prelude.ZValidation.{fail, succeed}

import scala.util.Try

/** An algorithm for calculating PCB distribution over a delivery panel.
  *
  * @param amount Number of PCBs to distribute
  * @param pcbSize PCB size
  * @param preferences Panel constraints
  */
class DistributionAlgorithm(
    amount: Int,
    pcbSize: PcbSize
) extends RectangleMesh
    with Logging {

  /** Calculates the distribution of PCBs on a panel
    * @param preferences          The user's panel preferences
    * @param numberOfAlternatives Number of alternative solutions along with the best solution. For now, alternative
    *                             solutions are not returned
    */
  private def calculateFromPreferences(
      preferences: PanelPreferences,
      numberOfAlternatives: Int = 5
  ): Either[PanelError, PanelDistribution] = {
    // To simplify the logic, we make sure that min/max panels are rotated in a way that width >= height
    val (minPanel, maxPanel) = preferences.harmonize

    // rotate PCB if needed that the width is the largest dimension
    val (pcbIsRotated, pcb) =
      if (pcbSize.heightInMm > pcbSize.widthInMm) {
        (true, pcbSize.swap)
      } else {
        (false, pcbSize)
      }

    val panel =
      if (pcb.widthInMm > maxPanel.croppedW) {
        // when any PCB dimension for rotated PCB is larger than the maximum allowed, no distribution is possible
        val errorKind =
          if (pcbIsRotated)
            PanelErrorKind.PcbHeightExceedsDeliveryPanel
          else
            PanelErrorKind.PcbWidthExceedsDeliveryPanel

        Left(PanelError(errorKind))
      } else if (pcb.heightInMm > maxPanel.croppedH) {
        val errorKind =
          if (pcbIsRotated)
            PanelErrorKind.PcbWidthExceedsDeliveryPanel
          else
            PanelErrorKind.PcbHeightExceedsDeliveryPanel

        Left(PanelError(errorKind))
      } else if (amount == 1 || preferences.maxPCBs.contains(1)) {
        Right(positionOnePCB(amount, minPanel, pcb, preferences, pcbIsRotated))
      } else
        Right(
          calculateDistribution(preferences, minPanel, maxPanel, pcb, pcbIsRotated)
            .map(distribution => positionSeveralPCBsCentered(distribution, preferences))
            // It's possible that a single PCB's size is smaller than the minimum panel preferences
            // This would rule out a 1x1 panel. Since we also exclude non-"perfect" panels, we can
            // end up in a situation where no possible solution exists. In this case, we fall back
            // to a 1x1 panel.
            .getOrElse(positionOnePCB(amount, minPanel, pcb, preferences, pcbIsRotated))
        )

    panel.map(_.rotate(preferences.maxWidth, preferences.maxHeight))
  }

  /** Distributes PCBs on a panel based on the panel details defined by the user
    * @param panelDetails      The generated (or user's) panel settings
    * @param panelConstraints  The PCB supplier's panel constraints (if they exist)
    */
  private def calculateFromPanelDetails(
      panelDetails: PanelDetails,
      panelConstraints: Option[PanelConstraints]
  ): Either[PanelError, PanelDistribution] = {
    val pcb =
      if (panelDetails.pcbIsRotated) {
        pcbSize.swap
      } else {
        pcbSize
      }

    Validation
      .validate(
        panelWidth(pcb, panelDetails, panelConstraints, panelDetails.pcbIsRotated),
        panelHeight(pcb, panelDetails, panelConstraints, panelDetails.pcbIsRotated)
      )
      .fold(
        error => Left(error.head),
        {
          case (panelWidth, panelHeight) =>
            val panelBound = PanelBound(
              w = panelWidth,
              h = panelHeight,
              padding = DistributionPanelPadding.fromPadding(panelDetails.padding),
              gap = new PanelGap(panelDetails.horizontalSpacingInMm, panelDetails.verticalSpacingInMm)
            )

            val wastedArea = WastedArea.calculate(
              amount = amount,
              rows = panelDetails.rowCount,
              panelDetails.columnCount,
              pcb = pcb,
              panelBound = panelBound
            )

            val mesh = PcbMesh(panelDetails.rowCount, panelDetails.columnCount)
            val items = positionSeveralPCBs(
              mesh = mesh,
              pcbSize = pcb,
              panelBound = panelBound
            )

            // Hack to avoid panel mismatch error
            val panelDistribution =
              // If there's a single panel, the computed gap will be always 0
              // (because there isn't a gap between a single panel)
              // To avoid the error, we use the configured gap
              if (mesh.size == 1) {
                PanelDistribution.withOverwrittenGap(
                  panel = panelBound.dimensions,
                  gap = panelBound.gap,
                  pcbPerPanel = panelDetails.quantity,
                  waste = wastedArea,
                  mesh = mesh,
                  items = items,
                  depanelization = panelDetails.depanelization,
                  minMillingDistanceInMm = panelDetails.minMillingDistanceInMm,
                  pcbIsRotated = panelDetails.pcbIsRotated
                )
              } else {
                // If there's more, then we compute as usual.
                PanelDistribution.withoutPrecomputedGap(
                  panel = panelBound.dimensions,
                  pcbPerPanel = panelDetails.quantity,
                  waste = wastedArea,
                  mesh = mesh,
                  items = items,
                  depanelization = panelDetails.depanelization,
                  minMillingDistanceInMm = panelDetails.minMillingDistanceInMm,
                  pcbIsRotated = panelDetails.pcbIsRotated
                )
              }

            Right(panelDistribution)
        }
      )
  }

  /** Returns a list of rectangles describing the position of PCB and padding strips
    */
  private def positionOnePCB(
      amount: Int,
      minPanel: PanelBound,
      pcb: PcbSize,
      preferences: PanelPreferences,
      pcbIsRotated: Boolean
  ): PanelDistribution = {
    val x =
      if (pcb.widthInMm > minPanel.croppedW) {
        minPanel.padding.leftInMm
      } else {
        (minPanel.w - pcb.widthInMm) / 2
      }
    val y =
      if (pcb.heightInMm > minPanel.croppedH) {
        minPanel.padding.topInMm
      } else {
        (minPanel.h - pcb.heightInMm) / 2
      }
    val panelW =
      if (pcb.widthInMm > minPanel.croppedW) {
        pcb.widthInMm + minPanel.padding.leftInMm + minPanel.padding.rightInMm
      } else {
        minPanel.w
      }
    val panelH =
      if (pcb.heightInMm > minPanel.croppedH) {
        pcb.heightInMm + minPanel.padding.topInMm + minPanel.padding.bottomInMm
      } else {
        minPanel.h
      }
    val pcbPosition =
      DistributionRectangle(Dimension(BigPoint(x, y), BigPoint(x + pcb.widthInMm, y + pcb.heightInMm)), Pcb)
    val resizedPanel = minPanel.copy(w = panelW, h = panelH)
    val padding      = getPadding(panelW, panelH, minPanel.padding)
    val items        = padding :+ pcbPosition
    val waste        = WastedArea(resizedPanel.crop.area - pcb.area, 0, amount)

    PanelDistribution.withoutPrecomputedGap(
      panel = resizedPanel.dimensions,
      pcbPerPanel = 1,
      mesh = PcbMesh(1, 1),
      waste = waste,
      items = items,
      depanelization = preferences.depanelization,
      minMillingDistanceInMm = 8, // ????
      pcbIsRotated = pcbIsRotated
    )
  }

  type Rotated = Boolean

  private def positionSeveralPCBsCentered(
      distributionSet: DistributionSet,
      preferences: PanelPreferences
  ): PanelDistribution = {
    val (mesh, rectangles) = positionSeveralPCBsCentered(
      distributionSet.pcbPerPanel,
      distributionSet.pcb,
      distributionSet.panel
    )

    PanelDistribution.withoutPrecomputedGap(
      panel = distributionSet.panel.dimensions,
      pcbPerPanel = distributionSet.pcbPerPanel,
      waste = distributionSet.wastedArea,
      mesh = mesh,
      items = rectangles,
      depanelization = preferences.depanelization,
      minMillingDistanceInMm = 8, // ???
      pcbIsRotated = distributionSet.pcbIsRotated
    )
  }

  /** Positions several PCBs on a panel, making sure that they are centered on the panel
    */
  private def positionSeveralPCBsCentered(
      amount: Int,
      pcbSize: PcbSize,
      panel: PanelBound
  ): (PcbMesh, Seq[DistributionRectangle]) = {
    val maxCols       = panel.cols(pcbSize.widthInMm).max(1) // just in case. otherwise we can have a / 0 exception
    val additionalRow = if (amount % maxCols != 0) 1 else 0
    val rows          = if (amount <= maxCols) 1 else amount / maxCols + additionalRow
    val cols          = if (amount <= maxCols) amount else maxCols
    // center the PCBs in the panel
    val minX = (panel.w - netWidth(pcbSize, panel.gap, cols)) / 2
    val minY = (panel.h - netHeight(pcbSize, panel.gap, rows)) / 2
    val PCBs = (0 until rows).flatMap { row =>
      (0 until cols).map { col =>
        val x = minX + netWidth(pcbSize, panel.gap, col) + panel.gap.x
        val y = minY + netHeight(pcbSize, panel.gap, row) + panel.gap.y
        DistributionRectangle(BigPoint(x, y), BigPoint(x + pcbSize.widthInMm, y + pcbSize.heightInMm), Pcb)
      }
    }

    val mesh    = PcbMesh(rows, cols)
    val padding = getPadding(panel.w, panel.h, panel.padding)
    val items   = padding ++ PCBs.take(amount)

    (mesh, items)
  }

  private def positionSeveralPCBs(
      mesh: PcbMesh,
      pcbSize: PcbSize,
      panelBound: PanelBound
  ): Seq[DistributionRectangle] = {
    // center the PCBs in the panel
    val PCBs = (0 until mesh.rows).flatMap { row =>
      (0 until mesh.columns).map { col =>
        val x = panelBound.padding.leftInMm + netWidth(pcbSize, panelBound.gap, col) + panelBound.gap.x
        val y = panelBound.padding.topInMm + netHeight(pcbSize, panelBound.gap, row) + panelBound.gap.y
        DistributionRectangle(BigPoint(x, y), BigPoint(x + pcbSize.widthInMm, y + pcbSize.heightInMm), Pcb)
      }
    }

    val padding = getPadding(panelBound.w, panelBound.h, panelBound.padding)
    val items   = padding ++ PCBs.take(mesh.size)

    items
  }

  /** Returns the panel size and the number of PCBs per panel
    */
  private def calculateDistribution(
      preferences: PanelPreferences,
      minPanel: PanelBound,
      maxPanel: PanelBound,
      pcb: PcbSize,
      pcbIsRotated: Boolean
  ): Option[DistributionSet] = {
    // if the maximum number of PCBs is 0, there is no limit
    val maxPCBs = preferences.maxPCBs.filter(_ > 0)

    val (croppedMinPanel, croppedMaxPanel) = (minPanel.crop, maxPanel.crop)

    val min  = croppedMinPanel.mesh(pcb)
    val minR = croppedMinPanel.mesh(pcb.swap)

    // maximum number of PCBs on the minimum sized panel. In case we have fewer PCBs than this,
    // we can use the minimum sized panel
    val (minN, minRotated) = if (min.size >= minR.size) (min.size, false) else (minR.size, true)

    maxPCBs match {
      case Some(pcbPerPanel) if pcbPerPanel <= minN =>
        // if the maximum number of PCBs per panel is smaller than the maximum number of PCBs, we must use the minimum sized panel
        Some(
          DistributionSet(
            pcb = if (minRotated) pcb.swap else pcb,
            amount = Left(pcbPerPanel),
            panel = minPanel,
            wastedArea = WastedArea(0, 0, 1),
            pcbIsRotated = pcbIsRotated
          )
        )
      case _ =>
        val max  = croppedMaxPanel.mesh(pcb)
        val maxR = croppedMaxPanel.mesh(pcb.swap)

        // maximum number of PCBs on the maximum sized panel. We cannot add more than this as no rotation is allowed
        val (maxN, maxRotated) = if (max.size >= maxR.size) (max.size, false) else (maxR.size, true)

        // we limit an optimum number of PCBs per panel between [minN, limit] as
        //  minN is the most effective way to distribute PCBs on the smallest panel
        //  and `limit` is the maximum number of PCBs that can be distributed on the largest panel
        val limit = Math.min(maxPCBs.getOrElse(maxN), maxN)
        val minP  = new PanelDimensions(croppedMinPanel.w, croppedMinPanel.h)
        val maxP  = new PanelDimensions(croppedMaxPanel.w, croppedMaxPanel.h)

        def toSet(data: (Int, Int, WastedArea), pcbIsRotated: Rotated): DistributionSet = {
          val (rows, cols, waste) = data
          val width = netWidth(pcb, preferences.spacing, cols) + minPanel.padding.leftInMm + minPanel.padding.rightInMm
          val height =
            netHeight(pcb, preferences.spacing, rows) + minPanel.padding.topInMm + minPanel.padding.bottomInMm
          val panel = minPanel.copy(w = width.max(minPanel.w), h = height.max(minPanel.h))
          DistributionSet(
            pcb = if (pcbIsRotated) pcb.swap else pcb,
            amount = Right(PcbMesh(rows, cols)),
            panel = panel,
            wastedArea = waste,
            pcbIsRotated = pcbIsRotated
          )
        }

        /** First we order sets by the smallest waste area, then by the largest number of PCBs
          */
        def order(left: DistributionSet, right: DistributionSet): Boolean =
          (left.wastedArea.totalInSqm, right.wastedArea.totalInSqm) match {
            case (l, r) if l < r => true
            case (l, r) if l > r => false
            case _               => left.pcbPerPanel > right.pcbPerPanel
          }

        val notRotated = calculateWastedArea(
          preferences = preferences,
          pcb = pcb,
          pcbLimit = limit,
          minPanel = minP,
          maxPanel = maxP,
          minMesh = min.nonZero,
          maxMesh = max.nonZero
        ).map(toSet(_, pcbIsRotated = false))
        val rotated = calculateWastedArea(
          preferences = preferences,
          pcb = pcb,
          pcbLimit = limit,
          minPanel = minP,
          maxPanel = maxP,
          minMesh = minR.nonZero,
          maxMesh = maxR.nonZero
        ).map(toSet(_, pcbIsRotated = true))

        val distribution = (notRotated ++ rotated).sortWith(order)

        distribution.headOption
    }
  }

  /** The main method that goes through all allowed combinations of rows and cols, and calculates the wasted area
    */
  private def calculateWastedArea(
      preferences: PanelPreferences,
      pcb: PcbSize,
      pcbLimit: Int,
      minPanel: PanelDimensions,
      maxPanel: PanelDimensions,
      minMesh: PcbMesh,
      maxMesh: PcbMesh
  ) = {
    val rows = Range(minMesh.rows, maxMesh.rows + 1)

    Range(minMesh.columns, maxMesh.columns + 1)
      .flatMap { cols =>
        rows.flatMap { rows =>
          val waste =
            if (rows * cols > pcbLimit)
              None
            else
              WastedArea.calculate(amount, rows, cols, pcb, preferences.spacing, minPanel, maxPanel)

          waste.map(waste => (rows, cols, waste))
        }
          // only use "perfect" panels, ie panels where the requested amount fits.
          // if none do, there will always be the 1x1 panel, which will fit
          .filter(x => amount % (x._1 * x._2) == 0)
      }
  }

  /** Returns padding strips for a panel
    * @param w width of the panel
    * @param h height of the panel
    * @param padding panel padding
    */
  private def getPadding(
      w: Millimeters,
      h: Millimeters,
      padding: DistributionPanelPadding
  ): Seq[DistributionRectangle] =
    Seq(
      DistributionRectangle(BigPoint(0, 0), BigPoint(w, padding.topInMm), Padding),
      DistributionRectangle(BigPoint(0, padding.topInMm), BigPoint(padding.leftInMm, h), Padding),
      DistributionRectangle(BigPoint(padding.leftInMm, h - padding.bottomInMm), BigPoint(w, h), Padding),
      DistributionRectangle(
        BigPoint(w - padding.rightInMm, padding.topInMm),
        BigPoint(w, h - padding.bottomInMm),
        Padding
      )
    ).filter(_.rect.surfaceArea > 0)

  private def panelGap(panelDetails: PanelDetails) =
    new PanelGap(panelDetails.horizontalSpacingInMm, panelDetails.verticalSpacingInMm)

  private def panelWidth(
      pcb: PcbSize,
      panelDetails: PanelDetails,
      panelConstraints: Option[PanelConstraints],
      pcbIsRotated: Boolean
  ): Validation[PanelError, BigDecimal] = {
    val paddingX = panelDetails.padding.leftInMm + panelDetails.padding.rightInMm
    val width    = netWidth(pcb, panelGap(panelDetails), panelDetails.columnCount) + paddingX
    assert(paddingX >= 0)
    assert(width > 0)

    panelConstraints match {
      case Some(panelConstraints) if width < panelConstraints.minWidth =>
        if (pcbIsRotated) {
          fail(PanelError(PanelErrorKind.PcbHeightBelowDeliveryPanelMinimum))
        } else {
          fail(PanelError(PanelErrorKind.PcbWidthBelowDeliveryPanelMinimum))
        }

      case Some(panelConstraints) if width > panelConstraints.maxWidth =>
        if (pcbIsRotated) {
          fail(PanelError(PanelErrorKind.PcbHeightExceedsDeliveryPanel))
        } else {
          fail(PanelError(PanelErrorKind.PcbWidthExceedsDeliveryPanel))
        }

      case _ => succeed(width)
    }
  }

  private def panelHeight(
      pcb: PcbSize,
      panelDetails: PanelDetails,
      panelConstraints: Option[PanelConstraints],
      pcbIsRotated: Boolean
  ): Validation[PanelError, BigDecimal] = {
    val paddingY = panelDetails.padding.topInMm + panelDetails.padding.bottomInMm
    val height   = netHeight(pcb, panelGap(panelDetails), panelDetails.rowCount) + paddingY
    assert(paddingY >= 0)
    assert(height > 0)

    panelConstraints match {
      case Some(panelConstraints) if height < panelConstraints.minHeight =>
        if (pcbIsRotated) {
          fail(PanelError(PanelErrorKind.PcbWidthBelowDeliveryPanelMinimum))
        } else {
          fail(PanelError(PanelErrorKind.PcbHeightBelowDeliveryPanelMinimum))
        }

      case Some(panelConstraints) if height > panelConstraints.maxHeight =>
        if (pcbIsRotated) {
          fail(PanelError(PanelErrorKind.PcbWidthExceedsDeliveryPanel))
        } else {
          fail(PanelError(PanelErrorKind.PcbHeightExceedsDeliveryPanel))
        }

      case _ => succeed(height)
    }
  }
}

object DistributionAlgorithm extends Logging {
  def calculate(
      amount: Int,
      pcbSize: PcbSize,
      preferences: PanelPreferences
  ): Either[PanelError, PanelDistribution] =
    handleError {
      new DistributionAlgorithm(amount, pcbSize).calculateFromPreferences(preferences)
    }

  def calculateDistributionFromDetails(
      amount: Int,
      pcbSize: PcbSize,
      panelDetails: PanelDetails,
      panelConstraints: Option[PanelConstraints]
  ): Either[PanelError, PanelDistribution] =
    handleError {
      new DistributionAlgorithm(amount, pcbSize)
        .calculateFromPanelDetails(panelDetails, panelConstraints)
    }

  private def handleError[T](
      fn: => Either[PanelError, T]
  ): Either[PanelError, T] =
    Try {
      fn
    }.toEither match {
      case Right(result) => result
      case Left(error) =>
        val span = Span.current()
        span.recordException(error)
        span.setStatus(StatusCode.ERROR, error.getMessage)
        logger.error(s"Error calculating PCB panel distribution: ${error.getMessage}")
        Left(PanelError(PanelErrorKind.UnknownError(error)))
    }
}
