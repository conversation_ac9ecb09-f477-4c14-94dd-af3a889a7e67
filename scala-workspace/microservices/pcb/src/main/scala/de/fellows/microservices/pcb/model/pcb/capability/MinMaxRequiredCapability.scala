package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.{Lang, Messages}
import de.fellows.microservices.pcb.model.pcb.{PropertyError, PropertyErrorKind}
import de.fellows.microservices.pcb.model.pcb.props.PCBRequiredProperty
import zio.prelude.Validation
import zio.prelude.Validation._

import java.text.{DecimalFormat, DecimalFormatSymbols}
import scala.math.Ordered.orderingToOrdered
import scala.reflect.ClassTag

/** PCB manufacture capability for mix/max limited property
  *
  * @param min
  *   Minimum supported value of the property
  * @param max
  *   Maximum supported value of the property
  */
case class MinMaxRequiredCapability[T <: PCBRequiredProperty[K], K](min: K, max: K)(implicit
    numeric: Numeric[K],
    override val typeTag: ClassTag[T]
) extends NonEmptyRequiredCapability[T, MinMaxRequiredCapability[T, K]] {

  /** Validates the property value against the capability
    */
  def validate(property: T)(implicit lang: Lang): Validation[PropertyError, T] =
    property.value match {
      case v if v < min =>
        fail(PropertyError(
          property,
          Messages("pcb.error.capability.min", Messages(property.label), df.format(min), property.unit),
          PropertyErrorKind.BelowMin(numeric.toDouble(v), numeric.toDouble(min), property.unit)
        ))
      case v if v > max =>
        fail(PropertyError(
          property,
          Messages("pcb.error.capability.max", Messages(property.label), df.format(max), property.unit),
          PropertyErrorKind.AboveMax(numeric.toDouble(v), numeric.toDouble(max), property.unit)
        ))
      case _ => succeed(property)
    }

  private val dfs = new DecimalFormatSymbols()
  dfs.setDecimalSeparator('.')
  dfs.setGroupingSeparator(',')
  private val df = new DecimalFormat("0.##", dfs)
}
