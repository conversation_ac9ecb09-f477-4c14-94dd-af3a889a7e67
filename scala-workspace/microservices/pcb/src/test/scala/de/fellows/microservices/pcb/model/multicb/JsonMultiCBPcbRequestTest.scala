package de.fellows.microservices.pcb.model.multicb

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.ViaFillingType
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.ApiService
import de.fellows.microservices.pcb.model.panel.PanelPreferences
import de.fellows.microservices.pcb.model.pcb.props
import org.scalatest.EitherValues
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import play.api.libs.json.Json

import java.time.LocalDateTime

class JsonMultiCBPcbRequestTest extends AnyWordSpec with Matchers with EitherValues {
  "Json conversion" should {
    "work" in {
      implicit val lang: Lang = Lang.Default
      val pcb = helper.defaultPcb.copy(
        properties = helper.defaultPcb.properties.copy(
          mechanical = helper.defaultPcb.properties.mechanical.copy(
            minViaDiameter = props.MinViaDiameter(0.31),
            viaFillingType = props.ViaFillingType(ViaFillingType.Filled)
          )
        )
      )
      val calculatedPanelInfo = ApiService.generateNewPanelFromPreferences(
        quantity = 1,
        pcbWidth = pcb.properties.basic.boardWidth.value,
        pcbHeight = pcb.properties.basic.boardHeight.value,
        panelPreferences = PanelPreferences.Empty,
        panelConstraints = None
      ).value

      val pcbRequest = MultiCbPcbRequest.convert(pcb, calculatedPanelInfo).value
      val json       = Json.toJson(pcbRequest)

      val expectedRaw =
        """
          |{
          |  "article_type" : "pcb",
          |  "name" : "PCB name",
          |  "layers" : 4,
          |  "size" : {
          |    "x" : {
          |      "unit" : "mm",
          |      "value" : 300.34
          |    },
          |    "y" : {
          |      "unit" : "mm",
          |      "value" : 150.55
          |    }
          |  },
          |  "quantity" : 1,
          |  "production_time" : 2,
          |  "surface_finish" : "hal lead-free",
          |  "min_track" : {
          |    "unit" : "µm",
          |    "value" : 100
          |  },
          |  "drills" : {
          |    "min_drills" : {
          |      "unit" : "mm",
          |      "value" : 0.3
          |    }
          |  },
          |  "material" : {
          |    "thickness" : {
          |      "unit" : "mm",
          |      "value" : 2
          |    },
          |    "type" : "fr4"
          |  },
          |  "panel" : {
          |    "panel_type" : "multiplier",
          |    "no_x_out" : false,
          |    "mechanical_treatment" : "scored"
          |  },
          |  "copper" : {
          |    "inner" : {
          |      "unit" : "µm",
          |      "value" : 35
          |    },
          |    "outer" : {
          |      "unit" : "µm",
          |      "value" : 105
          |    }
          |  },
          |  "solderstop" : {
          |    "bot" : "green"
          |  },
          |  "legend_print" : {
          |    "bot" : "white"
          |  },
          |  "peelable_mask" : {
          |    "bot" : false,
          |    "top" : false
          |  },
          |  "e_test" : true,
          |  "sideplating" : true,
          |  "blind_vias" : false,
          |  "buried_vias" : false,
          |  "via_filling" : "filled",
          |  "pressfit" : false,
          |  "goldfingers" : false,
          |  "layer_buildup" : "n/d",
          |  "ipc_class" : 2
          |}
          |""".stripMargin

      json should be(Json.parse(expectedRaw))
    }

    "work for quote requests" in {
      implicit val lang: Lang = Lang.Default
      val pcb = helper.defaultPcb.copy(
        properties = helper.defaultPcb.properties.copy(
          mechanical = helper.defaultPcb.properties.mechanical.copy(
            viaFillingType = props.ViaFillingType(ViaFillingType.Filled)
          ),
          layer = helper.defaultPcb.properties.layer.copy(
            finalThickness = props.FinalThickness.Thickness16mm
          )
        )
      )
      val calculatedPanelInfo = ApiService.generateNewPanelFromPreferences(
        quantity = 1,
        pcbWidth = pcb.properties.basic.boardWidth.value,
        pcbHeight = pcb.properties.basic.boardHeight.value,
        panelPreferences = PanelPreferences.Empty,
        panelConstraints = None
      ).value

      val pcbRequest = MultiCbPcbRequest.convert(pcb, calculatedPanelInfo).value
      val quote = MultiCbPcbQuote(
        pcb = pcbRequest,
        additionalWorkdays = Some(Seq(1, 2, 3)),
        additionalQuantities = Some(Seq(2, 3))
      )
      val quoteRequest = MultiCbQuoteRequest(Seq(quote))
      val json         = Json.toJson(quoteRequest)

      val expectedRaw =
        """
          |{
          |  "circuit_boards" : [ {
          |    "pcb" : {
          |      "article_type" : "pcb",
          |      "name" : "PCB name",
          |      "layers" : 4,
          |      "size" : {
          |        "x" : {
          |          "unit" : "mm",
          |          "value" : 300.34
          |        },
          |        "y" : {
          |          "unit" : "mm",
          |          "value" : 150.55
          |        }
          |      },
          |      "quantity" : 1,
          |      "production_time" : 2,
          |      "surface_finish" : "hal lead-free",
          |      "min_track" : {
          |        "unit" : "µm",
          |        "value" : 100
          |      },
          |      "drills" : {
          |        "min_drills" : {
          |          "unit" : "mm",
          |          "value" : 0.3
          |        }
          |      },
          |      "material" : {
          |        "thickness" : {
          |          "unit" : "mm",
          |          "value" : 1.55
          |        },
          |        "type" : "fr4"
          |      },
          |      "panel" : {
          |        "panel_type" : "multiplier",
          |        "no_x_out" : false,
          |        "mechanical_treatment" : "scored"
          |      },
          |      "copper" : {
          |        "inner" : {
          |          "unit" : "µm",
          |          "value" : 35
          |        },
          |        "outer" : {
          |          "unit" : "µm",
          |          "value" : 105
          |        }
          |      },
          |      "solderstop" : {
          |        "bot" : "green"
          |      },
          |      "legend_print" : {
          |        "bot" : "white"
          |      },
          |      "peelable_mask" : {
          |        "bot" : false,
          |        "top" : false
          |      },
          |      "e_test" : true,
          |      "sideplating" : true,
          |      "blind_vias" : false,
          |      "buried_vias" : false,
          |      "via_filling" : "filled",
          |      "pressfit" : false,
          |      "goldfingers" : false,
          |      "layer_buildup" : "n/d",
          |      "ipc_class" : 2
          |    },
          |    "additional_workdays" : [ 1, 2, 3 ],
          |    "additional_quantities" : [ 2, 3 ]
          |  } ]
          |}
          |""".stripMargin

      json should be(Json.parse(expectedRaw))
    }
  }

  "Json parsing" should {
    "convert valid requests" in {
      val raw =
        """
          |{
          |  "single_price": 2.9,
          |  "total_price": 290,
          |  "quantity": 100,
          |  "production_time": 5,
          |  "estimated_dispatch": "2025-01-24T00:00:00+01:00"
          |}
          |""".stripMargin

      Json.parse(raw).as[MultiCbPriceResponse] should be(
        MultiCbPriceResponse(
          singlePrice = 2.9,
          totalPrice = 290,
          quantity = 100,
          productionTime = 5,
          estimatedDispatch = LocalDateTime.of(2025, 1, 24, 0, 0)
        )
      )
    }
    "convert error responses" in {
      val raw =
        """
          |{
          |  "code": 400,
          |  "message": "A Problem occurred during article Validation",
          |  "validation_messages": [
          |    {
          |      "validation_message_severity": "Error",
          |      "message": "<b>1 WD Express</b> is currently only available with surface finish <b>HAL lead-free.</b>"
          |    }
          |  ]
          |}
          |""".stripMargin

      Json.parse(raw).as[MultiCbErrorResponse] should be(
        MultiCbErrorResponse(
          code = Some(400),
          message = Some("A Problem occurred during article Validation"),
          validationMessages = Some(Seq(
            MultiCbArticleValidationMessage(
              validationMessageSeverity = Some(MultiCbValidationMessageSeverity.Error),
              message =
                Some("<b>1 WD Express</b> is currently only available with surface finish <b>HAL lead-free.</b>"),
              details = None
            )
          ))
        )
      )
    }
  }
}
