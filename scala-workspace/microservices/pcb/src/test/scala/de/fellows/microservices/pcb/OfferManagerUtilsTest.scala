package de.fellows.microservices.pcb

import de.fellows.ems.pcb.api.specification.BaseMaterial.FR4
import de.fellows.microservices.pcb.model.RequestValidation
import de.fellows.microservices.pcb.model.pcb.props.{BaseMaterial, BoardHeight, BoardWidth, FinalThickness}
import de.fellows.microservices.pcb.model.pcb.{
  Manufacturer,
  ManufacturerApi,
  ManufacturerLocation,
  PropertyError,
  PropertyErrorKind
}
import de.fellows.microservices.pcb.offers.OfferManager
import de.fellows.utils.Region
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.util.UUID

class OfferManagerUtilsTest extends AnyFlatSpec with should.Matchers {
  "property errors" should "combine single errors" in {

    val errors = Seq(
      PropertyErrors(
        errors = Seq(
          PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
          PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
        )
      )
    )

    OfferManager.combine(errors) should be(
      Some(
        PropertyErrors(
          Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
          )
        )
      )
    )
  }

  it should "combine multiple errors" in {
    val errors = Seq(
      PropertyErrors(
        errors = Seq(
          PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
          PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
        )
      ),
      PropertyErrors(
        errors = Seq(
          PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
          PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
        )
      )
    )

    val comb = OfferManager.combine(errors)
    comb should be(
      Some(
        PropertyErrors(
          Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2")),
            PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
            PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
          )
        )
      )
    )
  }

  it should "keep property errors" in {
    val errors = Seq(
      PropertyErrors(
        errors = Seq(
          PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
          PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
        )
      ),
      ServerError("error 3")
    )

    OfferManager.combine(errors) should be(
      Some(
        PropertyErrors(
          Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
          )
        )
      )
    )
  }

  it should "keep one server error" in {
    val errors = Seq(
      ServerError("error 1"),
      ServerError("error 2")
    )

    OfferManager.combine(errors) should be(
      Some(
        ServerError("error 1")
      )
    )
  }

  "validations" should "combine single errors" in {
    val supplier      = UUID.randomUUID()
    val stocklocation = UUID.randomUUID()
    val location = ManufacturerLocation(
      stockLocation = stocklocation,
      region = Region.Unknown
    )
    val validation = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = Manufacturer(
        supplier = supplier,
        locations = Seq(
          location
        ),
        name = "test"
      ),
      location = location,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
          )
        )
      )
    )
    val validations = Seq(
      validation
    )
    val r = OfferManager.combineValidations(
      validations
    )

    r should be(validations)
  }

  it should "combine multiple errors for the same stock location" in {
    val supplier      = UUID.randomUUID()
    val stocklocation = UUID.randomUUID()
    val location = ManufacturerLocation(
      stockLocation = stocklocation,
      region = Region.Unknown
    )
    val manufacturer = Manufacturer(
      supplier = supplier,
      locations = Seq(
        location
      ),
      name = "test"
    )
    val validation1 = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = manufacturer,
      location = location,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
          )
        )
      )
    )
    val validation2 = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = manufacturer,
      location = location,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
            PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
          )
        )
      )
    )
    val validations = Seq(
      validation1,
      validation2
    )
    val r: Seq[RequestValidation] = OfferManager.combineValidations(
      validations
    )

    r should be(Seq(
      RequestValidation(
        api = ManufacturerApi.Wuerth,
        manufacturer = manufacturer,
        location = location,
        validation = Some(
          PropertyErrors(
            errors = Seq(
              PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
              PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2")),
              PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
              PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
            )
          )
        )
      )
    ))
  }

  it should "keep errors for different stock location" in {
    val supplier       = UUID.randomUUID()
    val stocklocation1 = UUID.randomUUID()
    val stocklocation2 = UUID.randomUUID()
    val location1 = ManufacturerLocation(
      stockLocation = stocklocation1,
      region = Region.Unknown
    )
    val location2 = ManufacturerLocation(
      stockLocation = stocklocation2,
      region = Region.Asia
    )
    val manufacturer = Manufacturer(
      supplier = supplier,
      locations = Seq(
        location1,
        location2
      ),
      name = "test"
    )
    val validation1 = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = manufacturer,
      location = location1,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
          )
        )
      )
    )
    val validation2 = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = manufacturer,
      location = location2,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
            PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
          )
        )
      )
    )
    val validations = Seq(
      validation1,
      validation2
    )
    val r: Set[RequestValidation] = OfferManager.combineValidations(
      validations
    ).toSet

    r should be(Set(
      RequestValidation(
        api = ManufacturerApi.Wuerth,
        manufacturer = manufacturer,
        location = location1,
        validation = Some(
          PropertyErrors(
            errors = Seq(
              PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
              PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
            )
          )
        )
      ),
      RequestValidation(
        api = ManufacturerApi.Wuerth,
        manufacturer = manufacturer,
        location = location2,
        validation = Some(
          PropertyErrors(
            errors = Seq(
              PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
              PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
            )
          )
        )
      )
    ))
  }

  it should "keep errors for different suppliers location" in {
    val supplier1      = UUID.randomUUID()
    val supplier2      = UUID.randomUUID()
    val stocklocation1 = UUID.randomUUID()
    val stocklocation2 = UUID.randomUUID()
    val location1 = ManufacturerLocation(
      stockLocation = stocklocation1,
      region = Region.Unknown
    )
    val location2 = ManufacturerLocation(
      stockLocation = stocklocation2,
      region = Region.Asia
    )
    val manufacturer1 = Manufacturer(
      supplier = supplier1,
      locations = Seq(
        location1,
        location2
      ),
      name = "test1"
    )
    val manufacturer2 = Manufacturer(
      supplier = supplier2,
      locations = Seq(
        location1,
        location2
      ),
      name = "test2"
    )
    val validation1 = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = manufacturer1,
      location = location1,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
            PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
          )
        )
      )
    )
    val validation2 = RequestValidation(
      api = ManufacturerApi.Wuerth,
      manufacturer = manufacturer2,
      location = location2,
      validation = Some(
        PropertyErrors(
          errors = Seq(
            PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
            PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
          )
        )
      )
    )
    val validations = Seq(
      validation1,
      validation2
    )
    val r: Set[RequestValidation] = OfferManager.combineValidations(
      validations
    ).toSet

    r should be(Set(
      RequestValidation(
        api = ManufacturerApi.Wuerth,
        manufacturer = manufacturer1,
        location = location1,
        validation = Some(
          PropertyErrors(
            errors = Seq(
              PropertyError(BaseMaterial(FR4), "error 1", PropertyErrorKind.Plain("error 1")),
              PropertyError(BoardHeight(100), "error 2", PropertyErrorKind.Plain("error 2"))
            )
          )
        )
      ),
      RequestValidation(
        api = ManufacturerApi.Wuerth,
        manufacturer = manufacturer2,
        location = location2,
        validation = Some(
          PropertyErrors(
            errors = Seq(
              PropertyError(BoardWidth(200), "error 3", PropertyErrorKind.Plain("error 3")),
              PropertyError(FinalThickness(100), "error 4", PropertyErrorKind.Plain("error 4"))
            )
          )
        )
      )
    ))
  }

}
