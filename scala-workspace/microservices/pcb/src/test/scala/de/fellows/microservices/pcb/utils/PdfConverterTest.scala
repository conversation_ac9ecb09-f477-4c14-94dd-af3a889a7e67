package de.fellows.microservices.pcb.utils

import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.PCBV2Properties
import de.fellows.app.assembly.commons.ProjectType
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.pcb.props.CustomStackUp
import de.fellows.microservices.pcb.model.pcb.{PCB, PCBProperties, PcbTempPreviews}
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.AssemblyId
import de.fellows.microservices.pcb.utils.pdf.PdfConverter
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.nio.file.Files
import java.time.Instant
import java.util.UUID

/** The sole purpose of this test is to simplify the visual check of generated pdf
  */
class PdfConverterTest extends AnyFlatSpec with should.Matchers with EitherValues {

  "Generated PDF file" should "be correct" in {
    val id = UUID.randomUUID()
    val pcb = PCB(
      id = helper.uuid,
      assemblyId = AssemblyId(id),
      name = Some("test"),
      properties = PCBProperties(helper.properties, CustomStackUp.no),
      orderId = None,
      previews = None,
      hash = None,
      files = Seq.empty,
      projectType = ProjectType.NoFiles,
      original = PCBV2(
        id = helper.uuid.value,
        name = None,
        assembly = id,
        description = None,
        created = Instant.now,
        files = None,
        filesLocked = false,
        lifecycles = Seq(),
        orderId = None,
        outline = None,
        specifications = Seq(),
        properties = PCBV2Properties.EMPTY,
        customer = None,
        projectType = ProjectType.NoFiles
      )
    )
    val previews = PcbTempPreviews(front = None, rear = None)
    val file = PdfConverter.generateSpecification(
      pcb,
      previews
    )
    file match {
      case Some(file) =>
        Files.deleteIfExists(file.toPath)
        succeed
      case None => fail("PDF file was not generated")
    }
  }
}
