package de.fellows.microservices.pcb.model.safePcb

import de.fellows.ems.pcb.api.specification.IPC600Class.{IPC1, IPC2, IPC3}
import de.fellows.microservices.pcb.model.pcb.props.IPCA600Class
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbIPCAClassTest extends AnyFlatSpec with should.Matchers {

  "SafePcbIPCAClass" should "have value=CLASS_2" in {
    SafePcbIPCAClass2.value should be("CLASS_2")
  }

  it should "have value=CLASS_3" in {
    SafePcbIPCAClass3.value should be("CLASS_3")
  }

  it should "be of Class2 for IPC1" in {
    SafePcbIPCAClass.apply(IPCA600Class(IPC1)) should be(SafePcbIPCAClass2)
  }
  it should "be of Class2 for IPC2" in {
    SafePcbIPCAClass.apply(IPCA600Class(IPC2)) should be(SafePcbIPCAClass2)
  }
  it should "be of Class3 for IPC3" in {
    SafePcbIPCAClass.apply(IPCA600Class(IPC3)) should be(SafePcbIPCAClass3)
  }

}
