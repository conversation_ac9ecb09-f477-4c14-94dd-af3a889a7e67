package de.fellows.microservices.pcb.utils

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.io.File
import java.nio.file.{Files, Path}

class WebPDecoderTest extends AnyFlatSpec with should.Matchers {
  "WebP image" should "be converted to JPG" in {
    val from = new File(getClass.getResource("/images/bender.webp").toURI)
    val to = Files.createTempFile("bender", ".jpg").toFile
    WebPDecoder.transform(from, to) shouldBe true
    val compare = Files.readAllBytes(Path.of(getClass.getResource("/images/bender.jpg").toURI))
    val result = Files.readAllBytes(to.toPath)
    compare shouldBe result
    to.delete()
  }
}
