package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.microservices.pcb.model.pcb.props.FinalThickness
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class BetaLayoutThicknessTest extends AnyFlatSpec with should.Matchers {

  val thickness = FinalThickness(None)
  "Thickness" should "be Mm100 if PCB thickness is 0.5" in {
    val origin = FinalThickness(0.50)
    BetaLayoutThickness.converter(origin) shouldBe Some(Thickness1mm(origin))
  }
  it should "be Mm100 if PCB thickness is 100" in {
    val origin = FinalThickness(1.00)
    BetaLayoutThickness.converter(origin) shouldBe Some(Thickness1mm(origin))
  }
  it should "be Mm100 if PCB thickness is less than 100" in {
    val origin = FinalThickness(0.95)
    BetaLayoutThickness.converter(origin) shouldBe Some(Thickness1mm(origin))
  }
  it should "be Mm160 if PCB thickness is 155" in {
    val origin = FinalThickness(1.55)
    BetaLayoutThickness.converter(origin) shouldBe Some(Thickness1_6mm(origin))
  }
  it should "be Mm160 if PCB thickness is less than 155" in {
    val origin = FinalThickness(1.32)
    BetaLayoutThickness.converter(origin) shouldBe Some(Thickness1_6mm(origin))
  }
  it should "be not supported if PCB thickness is more than 160" in {
    BetaLayoutThickness.converter(FinalThickness.Thickness24mm) shouldBe None
  }

}
