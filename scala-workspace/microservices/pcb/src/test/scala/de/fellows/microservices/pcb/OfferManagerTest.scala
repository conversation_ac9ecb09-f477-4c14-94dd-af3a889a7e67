package de.fellows.microservices.pcb

import com.osinka.i18n.Lang
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api._
import de.fellows.app.assemby.api.enums.{AssemblyStatus, UIStatus}
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.price.api.CapabilitiesApi.{
  CapabilityCheckFailure,
  CapabilityCheckResult,
  StackratePricingFailedField
}
import de.fellows.app.price.api._
import de.fellows.app.quotation.{Quotation, QuotationItem, QuotationService, QuotationStatus}
import de.fellows.app.supplier.{PCBSupplierDescription, SupplierService}
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.{Customer<PERSON>anel, PanelService}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.luminovo.panel._
import de.fellows.luminovo.sourcing._
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.microservices.pcb.client.LagomServiceClient
import de.fellows.microservices.pcb.client.luminovo.api.{
  StackratePricingConnection,
  SupplierAndStockLocationResponse,
  SupplierResponse
}
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.panel.Tenants
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.props.{BoardHeight, FourLayers, TGValue}
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI
import de.fellows.microservices.pcb.offers.OfferManager
import de.fellows.microservices.pcb.utils.StubPCB
import de.fellows.utils.model.PCBId
import de.fellows.utils.{CurrencyCode, Region}
import org.scalatest.EitherValues
import org.scalatest.flatspec.AsyncFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.Json
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{Request, Response, StringBody}

import java.time.Instant
import java.util.UUID
import scala.collection.immutable.Seq
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._

class OfferManagerTest extends AsyncFlatSpec with should.Matchers with EitherValues {
  "OfferManager" should "return correct results for pricing breaks and pricing errors" in {
    val sourcingScenarioId                  = SourcingScenarioId(UUID.randomUUID())
    val supplierAndStockLocationId          = UUID.randomUUID()
    val supplierAndStockLocationId2         = UUID.randomUUID()
    val supplierAndStockLocationId3         = UUID.randomUUID()
    val supplierAndStockLocationId4         = UUID.randomUUID()
    val supplierAndStockLocationId5         = UUID.randomUUID()
    val supplierAndStockLocationId6         = UUID.randomUUID()
    val notApprovedSupplierAndStockLocation = UUID.randomUUID()
    val supplierId                          = UUID.randomUUID()
    val supplierId2                         = UUID.randomUUID()
    val supplierId3                         = UUID.randomUUID()
    val supplierId4                         = UUID.randomUUID()
    val supplierId5                         = UUID.randomUUID()
    val pcbId                               = PCBId(UUID.randomUUID())

    val stackrateLuminovoSupplier1Id    = UUID.randomUUID()
    val stackrateLuminovoSupplier2Id    = UUID.randomUUID()
    val stackrateLuminovoSupplier3Id    = UUID.randomUUID()
    val stackrateLuminovoSupplier4Id    = UUID.randomUUID()
    val stackrateCentralSupplierId      = UUID.randomUUID()
    val stackrateOtherTenantSupplierId  = UUID.randomUUID()
    val stackrateOtherTenantSupplier2Id = UUID.randomUUID()

    val stackrateLuminovoSupplier1 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-1",
      id = stackrateLuminovoSupplier1Id,
      lqReference = supplierAndStockLocationId
    )
    val stackrateLuminovoSupplier2 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-2",
      id = stackrateLuminovoSupplier2Id,
      lqReference = supplierAndStockLocationId2
    )
    val stackrateLuminovoSupplier3 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateLuminovoSupplier3Id,
      lqReference = supplierAndStockLocationId3
    )
    val stackrateLuminovoSupplier4 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateLuminovoSupplier4Id,
      lqReference = notApprovedSupplierAndStockLocation
    )
    val stackrateOtherTenantSupplier = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplierId,
      lqReference = supplierAndStockLocationId4
    )
    val stackrateOtherTenantSupplier2 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier2Id,
      lqReference = supplierAndStockLocationId5
    )

    val stubPcb         = StubPCB.getPcb(pcbId)
    val shareId         = UUID.randomUUID()
    val specificationId = stubPcb.original.specifications.head.id
    val tenant          = "test-luminovo"

    val assemblyReference = AssemblyReference(
      team = tenant,
      id = stubPcb.assemblyId.value,
      gid = Some("test-gid"),
      version = pcbId.value
    )

    val asyncBackend: AsyncBackend = SttpBackendStub
      .asynchronousFuture
      .whenRequestMatchesPartial {
        case r if uriMatches(r, s"/internal/ems/pcbsupplier/$tenant/suppliers") =>
          Response.ok(Right(Seq(
            stackrateLuminovoSupplier1,
            stackrateLuminovoSupplier2,
            stackrateLuminovoSupplier3,
            stackrateLuminovoSupplier4
          )))

        case r if uriMatches(r, "/internal/ems/pcbsupplier/central/suppliers") =>
          Response.ok(Right(Seq(
            PCBSupplierDescription(
              name = "test-stackrate-central-supplier",
              id = Some(stackrateCentralSupplierId),
              technologies = None,
              region = None,
              lqReference = Some(supplierAndStockLocationId6)
            )
          )))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier)))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier2)))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/$tenant/suppliers/$stackrateLuminovoSupplier1Id") =>
          Response.ok(Right(stackrateLuminovoSupplier1))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/$tenant/suppliers/$stackrateLuminovoSupplier2Id") =>
          Response.ok(Right(stackrateLuminovoSupplier2))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/$tenant/suppliers/$stackrateLuminovoSupplier3Id") =>
          Response.ok(Right(stackrateLuminovoSupplier3))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers/$stackrateOtherTenantSupplierId"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier2Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier2))

        case r if uriMatches(r, s"/internal/price/teams/$tenant/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing",
              id = UUID.randomUUID(),
              supplier = stackrateLuminovoSupplier1Id,
              pricings = Seq(
                DeployedDecision(team = "luminovo", key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-eu",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplierId,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizEu, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-apac",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplier2Id,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizApac, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/calculation") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/$tenant/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplierId,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "height",
                      variableName = "height",
                      value = "151",
                      expression = Some("> 150")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplier2Id,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "layerCount",
                      variableName = "layerCount",
                      value = "4",
                      expression = Some("> 2")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateCentralSupplierId,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "materialtg",
                      variableName = "materialtg",
                      value = "180",
                      expression = Some("< 180")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, s"/internal/user/teams/$tenant") =>
          Response.ok(Right(
            Team(
              domain = tenant,
              name = "Test Luminovo",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizEu}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizEu,
              name = "pcbwhiz eu",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizApac}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizApac,
              name = "pcbwhiz apac",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/customer/$tenant/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizEu}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizApac}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}/versions/$pcbId/shares"
            ) =>
          assert(r.body.isInstanceOf[StringBody])
          val team = Json.parse(r.body.asInstanceOf[StringBody].s).as[ShareAssemblyTo].team
          Response.ok(Right(
            SharedAssembly(
              team = team,
              id = shareId,
              ref = assemblyReference,
              created = Instant.now,
              information = SharedAssemblyInformation(
                uiStatus = UIStatus.DEFAULT,
                customer = None,
                assignee = None
              )
            )
          ))

        case r if uriMatches(r, s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}") =>
          Response.ok(Right(AssemblyWithShares(mkAssembly(pcbId), Seq.empty)))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/$tenant/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(
            Seq(
              CalculatedPrices(
                request =
                  PriceRequest(
                    count = 1,
                    delivery = 1,
                    nre = None,
                    assembly = None,
                    specification = None,
                    panel = None
                  ),
                pricings = Seq(
                  PricingBreak(
                    "test-pricing-break",
                    stackrateLuminovoSupplier1Id,
                    "something is not supported",
                    Seq.empty,
                    Seq.empty
                  ),
                  PricingError("test-pricing-error", stackrateLuminovoSupplier2Id, "something went wrong"),
                  CalculatedPricing(
                    name = "test-pricing-ok",
                    supplier = stackrateLuminovoSupplier3Id,
                    tables = Seq.empty,
                    price = BigDecimal(10),
                    unitPrice = BigDecimal(1),
                    sumDetails = DetailedSum(
                      flexSum = BigDecimal(10),
                      fixSum = BigDecimal(10),
                      nreSum = None
                    ),
                    currency = CurrencyCode.EUR
                  )
                )
              )
            )
          ))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizEu}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizApac}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/quotation/teams/$tenant/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizEu}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/$tenant/quotationitems") =>
          Response.ok(Right(QuotationItem(
            id = UUID.randomUUID(),
            assembly = assemblyReference,
            name = Some("some-quotation"),
            description = None,
            info = None
          )))

        case r if uriMatches(r, s"/internal/quotation/teams/$tenant/quotations") =>
          Response.ok(Right(Quotation(
            team = tenant,
            assembly = assemblyReference,
            quotationId = UUID.randomUUID(),
            customerId = UUID.randomUUID(),
            name = "quotation-1",
            externalID = None,
            description = None,
            requestID = None,
            requestBy = None,
            created = Instant.now,
            creator = UUID.randomUUID(),
            assignee = None,
            status = QuotationStatus.SENT(Instant.now),
            contactId = None,
            billing = None,
            shipping = None,
            items = Some(Seq()),
            publicNotes = None,
            validFor = None,
            requestDate = None
          )))

        case r
            if uriMatches(
              r,
              s"/internal/ems/panel/$tenant/customerpanels/assemblies/${assemblyReference.id}/versions/${assemblyReference.version}"
            ) =>
          Response.ok(
            Right(Seq[CustomerPanel]())
          )
      }

    val service = mkOfferManagerservice(stubPcb, asyncBackend)

    val request = BatchOfferRequest(
      pcbId = pcbId,
      tenant = tenant,
      api = ManufacturerApi.Stackrate,
      sourcingScenarioRequests = Seq(ScenarioRequest(
        sourcingScenarioId = sourcingScenarioId,
        leadTimes = Seq(LeadTime(LeadTimePreference.Fastest)),
        quantity = 100,
        change = ChangeStatus.New,
        supplierAndStockLocations = Seq(
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "some luminovo supplier",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Unknown
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationId2,
            supplier = SupplierResponse(
              id = supplierId2,
              name = "some luminovo supplier 2",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Unknown
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationId3,
            supplier = SupplierResponse(
              id = supplierId3,
              name = "some luminovo supplier 3",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Unknown
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationId4,
            supplier = SupplierResponse(
              id = supplierId4,
              name = "some supplier multiple regions",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Europe
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationId5,
            supplier = SupplierResponse(
              id = supplierId4,
              name = "some supplier multiple regions",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Asia
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationId6,
            supplier = SupplierResponse(
              id = supplierId5,
              name = "some central supplier",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Europe
          )
        )
      )),
      existingOffers = Seq.empty,
      credentials = None,
      panels = Seq(PerPcb(
        pcb = pcbId,
        panelDetails = PanelDetails(
          id = Some(PanelId(UUID.fromString("cdc0dbf5-49a1-4c11-b7a3-0ee60e5edba5"))),
          rowCount = 2,
          columnCount = 2,
          horizontalSpacingInMm = 2,
          verticalSpacingInMm = 2,
          minMillingDistanceInMm = 2,
          padding = LuminovoPadding(2, 2, 2, 2),
          depanelization = Depanelization.VCut,
          pcbIsRotated = false
        )
      )),
      stackratePricingConnections = Seq(
        StackratePricingConnection(tenant, Some(tenant), None),
        StackratePricingConnection(Tenants.PcbWhizEu, Some(tenant), None),
        StackratePricingConnection(Tenants.PcbWhizApac, Some(tenant), None)
      ),
      panelPreferenceSettings = PanelPreferenceSettings(panelPreferences = None)
    )

    service
      .makeBatchOffers(tenant, request)
      .map { result =>
        println(s"result: ${result}")
        result.value.pcbId shouldBe pcbId
        result.value.pcbHash shouldBe Some("test-hash")
        result.value.responses.sortBy(_.location.stockLocation) shouldBe Seq(
          ManufacturerStatus(
            api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
            manufacturer = Manufacturer(
              supplierId5,
              List(ManufacturerLocation(supplierAndStockLocationId6, Region.Europe)),
              "some central supplier"
            ),
            location = ManufacturerLocation(supplierAndStockLocationId6, Region.Europe),
            status = PcbOfferStatus.SpecificationUnsupported(List(PropertyError(
              TGValue(135),
              "180 is not supported because it is < 180",
              PropertyErrorKind.Plain("180 is not supported because it is < 180")
            )))
          ),
          ManufacturerStatus(
            api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
            manufacturer = Manufacturer(
              supplierId,
              List(ManufacturerLocation(supplierAndStockLocationId, Region.Unknown)),
              "some luminovo supplier"
            ),
            location = ManufacturerLocation(supplierAndStockLocationId, Region.Unknown),
            status = PcbOfferStatus.StackratePricingBreaks(List(PcbOfferStatus.StackratePricingMessage(
              "test-pricing-break",
              "something is not supported",
              Seq.empty,
              Seq.empty
            )))
          ),
          ManufacturerStatus(
            api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
            manufacturer = Manufacturer(
              supplierId2,
              List(ManufacturerLocation(supplierAndStockLocationId2, Region.Unknown)),
              "some luminovo supplier 2"
            ),
            location = ManufacturerLocation(supplierAndStockLocationId2, Region.Unknown),
            status = PcbOfferStatus.StackratePricingErrors(List(PcbOfferStatus.StackratePricingMessage(
              "test-pricing-error",
              "something went wrong",
              Seq.empty,
              Seq.empty
            )))
          ),
          ManufacturerStatus(
            api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
            manufacturer = Manufacturer(
              supplierId3,
              List(ManufacturerLocation(supplierAndStockLocationId3, Region.Unknown)),
              "some luminovo supplier 3"
            ),
            location = ManufacturerLocation(supplierAndStockLocationId3, Region.Unknown),
            status = PcbOfferStatus.Success(Seq.empty)
          ),
          ManufacturerStatus(
            api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
            manufacturer = Manufacturer(
              supplierId4,
              List(
                ManufacturerLocation(supplierAndStockLocationId5, Region.Asia),
                ManufacturerLocation(supplierAndStockLocationId4, Region.Europe)
              ).sortBy(_.stockLocation),
              "some supplier multiple regions"
            ),
            location = ManufacturerLocation(supplierAndStockLocationId4, Region.Europe),
            status = PcbOfferStatus.SpecificationUnsupported(List(PropertyError(
              BoardHeight(151.41000366210938),
              "151 is not supported because it is > 150",
              PropertyErrorKind.Plain("151 is not supported because it is > 150")
            )))
          ),
          ManufacturerStatus(
            api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
            manufacturer = Manufacturer(
              supplierId4,
              List(
                ManufacturerLocation(supplierAndStockLocationId5, Region.Asia),
                ManufacturerLocation(supplierAndStockLocationId4, Region.Europe)
              ).sortBy(_.stockLocation),
              "some supplier multiple regions"
            ),
            location = ManufacturerLocation(supplierAndStockLocationId5, Region.Asia),
            status = PcbOfferStatus.SpecificationUnsupported(List(PropertyError(
              FourLayers,
              "4 is not supported because it is > 2",
              PropertyErrorKind.Plain("4 is not supported because it is > 2")
            )))
          )
        ).sortBy(_.location.stockLocation)
      }
  }

  it should "handle locations with different statuses correctly" in {
    val sourcingScenarioId               = SourcingScenarioId(UUID.randomUUID())
    val supplierAndStockLocationId       = UUID.randomUUID()
    val supplierAndStockLocationEuropeId = UUID.randomUUID()
    val supplierAndStockLocationAsiaId   = UUID.randomUUID()
    val supplierId                       = UUID.randomUUID()
    val pcbId                            = PCBId(UUID.randomUUID())

    val stackrateOtherTenantSupplierId  = UUID.randomUUID()
    val stackrateOtherTenantSupplier2Id = UUID.randomUUID()
    val stackrateOtherTenantSupplier3Id = UUID.randomUUID()

    val stackrateOtherTenantSupplier = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplierId,
      lqReference = supplierAndStockLocationEuropeId
    )
    val stackrateOtherTenantSupplier2 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier2Id,
      lqReference = supplierAndStockLocationAsiaId
    )
    val stackrateOtherTenantSupplier3 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier3Id,
      lqReference = supplierAndStockLocationId
    )

    val stubPcb         = StubPCB.getPcb(pcbId)
    val shareId         = UUID.randomUUID()
    val specificationId = stubPcb.original.specifications.head.id
    val tenant          = "test-luminovo"

    val assemblyReference = AssemblyReference(
      team = tenant,
      id = stubPcb.assemblyId.value,
      gid = Some("test-gid"),
      version = pcbId.value
    )

    val asyncBackend: AsyncBackend = SttpBackendStub
      .asynchronousFuture
      .whenRequestMatchesPartial {
        case r if uriMatches(r, "/internal/ems/pcbsupplier/central/suppliers") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier)))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier2, stackrateOtherTenantSupplier3)))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers/$stackrateOtherTenantSupplierId"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier2Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier2))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier3Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier3))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-eu",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplierId,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizEu, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-apac",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplier2Id,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizApac, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/calculation") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplierId,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "height",
                      variableName = "height",
                      value = "151",
                      expression = Some("> 150")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, "/internal/price/teams/central/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizEu}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizEu,
              name = "pcbwhiz eu",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizApac}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizApac,
              name = "pcbwhiz apac",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizEu}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizApac}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}/versions/$pcbId/shares"
            ) =>
          assert(r.body.isInstanceOf[StringBody])
          val team = Json.parse(r.body.asInstanceOf[StringBody].s).as[ShareAssemblyTo].team
          Response.ok(Right(
            SharedAssembly(
              team = team,
              id = shareId,
              ref = assemblyReference,
              created = Instant.now,
              information = SharedAssemblyInformation(
                uiStatus = UIStatus.DEFAULT,
                customer = None,
                assignee = None
              )
            )
          ))

        case r if uriMatches(r, s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}") =>
          Response.ok(Right(AssemblyWithShares(mkAssembly(pcbId), Seq.empty)))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizEu}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizApac}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(
            Seq(
              CalculatedPrices(
                request =
                  PriceRequest(
                    count = 1,
                    delivery = 1,
                    nre = None,
                    assembly = None,
                    specification = None,
                    panel = None
                  ),
                pricings = Seq(
                  CalculatedPricing(
                    name = "test-pricing-ok",
                    supplier = stackrateOtherTenantSupplier2Id,
                    tables = Seq.empty,
                    price = BigDecimal(10),
                    unitPrice = BigDecimal(1),
                    sumDetails = DetailedSum(
                      flexSum = BigDecimal(10),
                      fixSum = BigDecimal(10),
                      nreSum = None
                    ),
                    currency = CurrencyCode.EUR
                  )
                )
              )
            )
          ))

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizEu}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotationitems") =>
          Response.ok(Right(QuotationItem(
            id = UUID.randomUUID(),
            assembly = assemblyReference,
            name = Some("some-quotation"),
            description = None,
            info = None
          )))

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations") =>
          Response.ok(Right(Quotation(
            team = tenant,
            assembly = assemblyReference,
            quotationId = UUID.randomUUID(),
            customerId = UUID.randomUUID(),
            name = "quotation-1",
            externalID = None,
            description = None,
            requestID = None,
            requestBy = None,
            created = Instant.now,
            creator = UUID.randomUUID(),
            assignee = None,
            status = QuotationStatus.SENT(Instant.now),
            contactId = None,
            billing = None,
            shipping = None,
            items = Some(Seq()),
            publicNotes = None,
            validFor = None,
            requestDate = None
          )))

        case r
            if uriMatches(
              r,
              s"/internal/ems/panel/$tenant/customerpanels/assemblies/${assemblyReference.id}/versions/${assemblyReference.version}"
            ) =>
          Response.ok(
            Right(Seq[CustomerPanel]())
          )
      }

    val service = mkOfferManagerservice(stubPcb, asyncBackend)

    val request = BatchOfferRequest(
      pcbId = pcbId,
      tenant = tenant,
      api = ManufacturerApi.Stackrate,
      sourcingScenarioRequests = Seq(ScenarioRequest(
        sourcingScenarioId = sourcingScenarioId,
        leadTimes = Seq(LeadTime(LeadTimePreference.Fastest)),
        quantity = 100,
        change = ChangeStatus.New,
        supplierAndStockLocations = Seq(
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationEuropeId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "pcbwhiz",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Europe
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationAsiaId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "pcbwhiz",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Asia
          )
        )
      )),
      existingOffers = Seq.empty,
      credentials = None,
      panels = Seq(PerPcb(
        pcb = pcbId,
        panelDetails = PanelDetails(
          id = Some(PanelId(UUID.fromString("cdc0dbf5-49a1-4c11-b7a3-0ee60e5edba5"))),
          rowCount = 2,
          columnCount = 2,
          horizontalSpacingInMm = 2,
          verticalSpacingInMm = 2,
          minMillingDistanceInMm = 2,
          padding = LuminovoPadding(2, 2, 2, 2),
          depanelization = Depanelization.VCut,
          pcbIsRotated = false
        )
      )),
      stackratePricingConnections =
        Seq(
          StackratePricingConnection(Tenants.PcbWhizEu, Some(tenant), None),
          StackratePricingConnection(Tenants.PcbWhizApac, Some(tenant), None)
        ),
      panelPreferenceSettings = PanelPreferenceSettings(panelPreferences = None)
    )

    service
      .makeBatchOffers(tenant, request)
      .map { result =>
        result.value.pcbId shouldBe pcbId
        result.value.pcbHash shouldBe Some("test-hash")
        result.value.responses.sortBy(_.location.stockLocation) shouldBe
          Seq(
            ManufacturerStatus(
              api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
              manufacturer = Manufacturer(
                supplierId,
                List(
                  ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
                  ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe)
                ).sortBy(_.stockLocation),
                "pcbwhiz"
              ),
              location = ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
              status = PcbOfferStatus.Success(Seq.empty)
            ),
            ManufacturerStatus(
              api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
              manufacturer = Manufacturer(
                supplierId,
                List(
                  ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
                  ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe)
                ).sortBy(_.stockLocation),
                "pcbwhiz"
              ),
              location = ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe),
              status = PcbOfferStatus.SpecificationUnsupported(List(PropertyError(
                BoardHeight(151.41000366210938),
                "151 is not supported because it is > 150",
                PropertyErrorKind.Plain("151 is not supported because it is > 150")
              )))
            )
          ).sortBy(_.location.stockLocation)
      }
  }

  it should "handle the same supplier location from different stackrate tenants" in {
    val sourcingScenarioId               = SourcingScenarioId(UUID.randomUUID())
    val supplierAndStockLocationEuropeId = UUID.randomUUID()
    val supplierAndStockLocationAsiaId   = UUID.randomUUID()
    val supplierId                       = UUID.randomUUID()
    val pcbId                            = PCBId(UUID.randomUUID())

    val stackrateOtherTenantSupplierId  = UUID.randomUUID()
    val stackrateOtherTenantSupplier2Id = UUID.randomUUID()
    val stackrateOtherTenantSupplier3Id = UUID.randomUUID()

    val stackrateOtherTenantSupplier = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplierId,
      lqReference = supplierAndStockLocationEuropeId
    )
    val stackrateOtherTenantSupplier2 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier2Id,
      lqReference = supplierAndStockLocationAsiaId
    )
    val stackrateOtherTenantSupplier3 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier3Id,
      lqReference = supplierAndStockLocationEuropeId
    )

    val stubPcb         = StubPCB.getPcb(pcbId)
    val shareId         = UUID.randomUUID()
    val specificationId = stubPcb.original.specifications.head.id
    val tenant          = "test-luminovo"

    val assemblyReference = AssemblyReference(
      team = tenant,
      id = stubPcb.assemblyId.value,
      gid = Some("test-gid"),
      version = pcbId.value
    )

    val asyncBackend: AsyncBackend = SttpBackendStub
      .asynchronousFuture
      .whenRequestMatchesPartial {
        case r if uriMatches(r, "/internal/ems/pcbsupplier/central/suppliers") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier, stackrateOtherTenantSupplier2)))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier3)))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers/$stackrateOtherTenantSupplierId"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier2Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier2))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier3Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier3))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-eu",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplierId,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizEu, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-apac",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplier2Id,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizApac, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/calculation") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplier3Id,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "materialtg",
                      variableName = "materialtg",
                      value = "180",
                      expression = Some("< 180")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplierId,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "height",
                      variableName = "height",
                      value = "151",
                      expression = Some("> 150")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizEu}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizEu,
              name = "pcbwhiz eu",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizApac}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizApac,
              name = "pcbwhiz apac",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizEu}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizApac}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}/versions/$pcbId/shares"
            ) =>
          assert(r.body.isInstanceOf[StringBody])
          val team = Json.parse(r.body.asInstanceOf[StringBody].s).as[ShareAssemblyTo].team
          Response.ok(Right(
            SharedAssembly(
              team = team,
              id = shareId,
              ref = assemblyReference,
              created = Instant.now,
              information = SharedAssemblyInformation(
                uiStatus = UIStatus.DEFAULT,
                customer = None,
                assignee = None
              )
            )
          ))

        case r if uriMatches(r, s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}") =>
          Response.ok(Right(AssemblyWithShares(mkAssembly(pcbId), Seq.empty)))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizEu}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizApac}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(
            Seq(
              CalculatedPrices(
                request =
                  PriceRequest(
                    count = 1,
                    delivery = 1,
                    nre = None,
                    assembly = None,
                    specification = None,
                    panel = None
                  ),
                pricings = Seq(
                  CalculatedPricing(
                    name = "test-pricing-ok",
                    supplier = stackrateOtherTenantSupplier2Id,
                    tables = Seq.empty,
                    price = BigDecimal(10),
                    unitPrice = BigDecimal(1),
                    sumDetails = DetailedSum(
                      flexSum = BigDecimal(10),
                      fixSum = BigDecimal(10),
                      nreSum = None
                    ),
                    currency = CurrencyCode.EUR
                  )
                )
              )
            )
          ))

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizEu}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotationitems") =>
          Response.ok(Right(QuotationItem(
            id = UUID.randomUUID(),
            assembly = assemblyReference,
            name = Some("some-quotation"),
            description = None,
            info = None
          )))

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations") =>
          Response.ok(Right(Quotation(
            team = tenant,
            assembly = assemblyReference,
            quotationId = UUID.randomUUID(),
            customerId = UUID.randomUUID(),
            name = "quotation-1",
            externalID = None,
            description = None,
            requestID = None,
            requestBy = None,
            created = Instant.now,
            creator = UUID.randomUUID(),
            assignee = None,
            status = QuotationStatus.SENT(Instant.now),
            contactId = None,
            billing = None,
            shipping = None,
            items = Some(Seq()),
            publicNotes = None,
            validFor = None,
            requestDate = None
          )))

        case r
            if uriMatches(
              r,
              s"/internal/ems/panel/$tenant/customerpanels/assemblies/${assemblyReference.id}/versions/${assemblyReference.version}"
            ) =>
          Response.ok(
            Right(Seq[CustomerPanel]())
          )
      }

    val service = mkOfferManagerservice(stubPcb, asyncBackend)

    val request = BatchOfferRequest(
      pcbId = pcbId,
      tenant = tenant,
      api = ManufacturerApi.Stackrate,
      sourcingScenarioRequests = Seq(ScenarioRequest(
        sourcingScenarioId = sourcingScenarioId,
        leadTimes = Seq(LeadTime(LeadTimePreference.Fastest)),
        quantity = 100,
        change = ChangeStatus.New,
        supplierAndStockLocations = Seq(
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationEuropeId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "pcbwhiz",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Europe
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationAsiaId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "pcbwhiz",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Asia
          )
        )
      )),
      existingOffers = Seq.empty,
      credentials = None,
      panels = Seq(PerPcb(
        pcb = pcbId,
        panelDetails = PanelDetails(
          id = Some(PanelId(UUID.fromString("cdc0dbf5-49a1-4c11-b7a3-0ee60e5edba5"))),
          rowCount = 2,
          columnCount = 2,
          horizontalSpacingInMm = 2,
          verticalSpacingInMm = 2,
          minMillingDistanceInMm = 2,
          padding = LuminovoPadding(2, 2, 2, 2),
          depanelization = Depanelization.VCut,
          pcbIsRotated = false
        )
      )),
      stackratePricingConnections =
        Seq(
          StackratePricingConnection(Tenants.PcbWhizEu, Some(tenant), None),
          StackratePricingConnection(Tenants.PcbWhizApac, Some(tenant), None)
        ),
      panelPreferenceSettings = PanelPreferenceSettings(panelPreferences = None)
    )

    service
      .makeBatchOffers(tenant, request)
      .map { result =>
        result.value.pcbId shouldBe pcbId
        result.value.pcbHash shouldBe Some("test-hash")
        result.value.responses.sortBy(_.location.stockLocation) shouldBe
          Seq(
            ManufacturerStatus(
              api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
              manufacturer = Manufacturer(
                supplierId,
                List(
                  ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
                  ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe)
                ).sortBy(_.stockLocation),
                "pcbwhiz"
              ),
              location = ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
              status = PcbOfferStatus.Success(Seq.empty)
            ),
            ManufacturerStatus(
              api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
              manufacturer = Manufacturer(
                supplierId,
                List(
                  ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
                  ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe)
                ).sortBy(_.stockLocation),
                "pcbwhiz"
              ),
              location = ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe),
              status = PcbOfferStatus.SpecificationUnsupported(List(PropertyError(
                TGValue(135),
                "180 is not supported because it is < 180",
                PropertyErrorKind.Plain("180 is not supported because it is < 180")
              )))
            )
          ).sortBy(_.location.stockLocation)
      }
  }

  it should "should return an error if a custom stackup is present" in {
    val sourcingScenarioId               = SourcingScenarioId(UUID.randomUUID())
    val supplierAndStockLocationEuropeId = UUID.randomUUID()
    val supplierAndStockLocationAsiaId   = UUID.randomUUID()
    val supplierId                       = UUID.randomUUID()
    val pcbId                            = PCBId(UUID.randomUUID())

    val stackrateOtherTenantSupplierId  = UUID.randomUUID()
    val stackrateOtherTenantSupplier2Id = UUID.randomUUID()
    val stackrateOtherTenantSupplier3Id = UUID.randomUUID()

    val stackrateOtherTenantSupplier = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplierId,
      lqReference = supplierAndStockLocationEuropeId
    )
    val stackrateOtherTenantSupplier2 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier2Id,
      lqReference = supplierAndStockLocationAsiaId
    )
    val stackrateOtherTenantSupplier3 = mkStackrateSupplier(
      name = "test-stackrate-luminovo-supplier-3",
      id = stackrateOtherTenantSupplier3Id,
      lqReference = supplierAndStockLocationEuropeId
    )

    val stubPcb = {
      val original = StubPCB.getPcb(pcbId)
      original.copy(properties = original.properties.withCustomStackUp)
    }
    val shareId         = UUID.randomUUID()
    val specificationId = stubPcb.original.specifications.head.id
    val tenant          = "test-luminovo"

    val assemblyReference = AssemblyReference(
      team = tenant,
      id = stubPcb.assemblyId.value,
      gid = Some("test-gid"),
      version = pcbId.value
    )

    val asyncBackend: AsyncBackend = SttpBackendStub
      .asynchronousFuture
      .whenRequestMatchesPartial {
        case r if uriMatches(r, "/internal/ems/pcbsupplier/central/suppliers") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier, stackrateOtherTenantSupplier2)))

        case r if uriMatches(r, s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers") =>
          Response.ok(Right(Seq(stackrateOtherTenantSupplier3)))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizEu}/suppliers/$stackrateOtherTenantSupplierId"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier2Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier2))

        case r
            if uriMatches(
              r,
              s"/internal/ems/pcbsupplier/${Tenants.PcbWhizApac}/suppliers/$stackrateOtherTenantSupplier3Id"
            ) =>
          Response.ok(Right(stackrateOtherTenantSupplier3))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-eu",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplierId,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizEu, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/calculation") =>
          Response.ok(Right(Seq(
            DeployedPricing(
              name = "test-pricing-pcbwhiz-apac",
              id = UUID.randomUUID(),
              supplier = stackrateOtherTenantSupplier2Id,
              pricings = Seq(
                DeployedDecision(team = Tenants.PcbWhizApac, key = "??")
              ),
              enableApi = true,
              currency = None
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/calculation") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizEu}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq.empty))

        case r if uriMatches(r, s"/internal/price/teams/${Tenants.PcbWhizApac}/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplier3Id,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "materialtg",
                      variableName = "materialtg",
                      value = "180",
                      expression = Some("< 180")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, "/internal/price/teams/central/capabilitycheck/bypcb") =>
          Response.ok(Right(Seq(
            CapabilityCheckResult(
              CapabilitiesDeploymentDescriptor(
                supplier = stackrateOtherTenantSupplierId,
                tables = Seq.empty
              ),
              failures = Seq(
                CapabilityCheckFailure(
                  hint = None,
                  variableFailures = Seq(
                    StackratePricingFailedField(
                      variable = "height",
                      variableName = "height",
                      value = "151",
                      expression = Some("> 150")
                    )
                  )
                )
              )
            )
          )))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizEu}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizEu,
              name = "pcbwhiz eu",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/user/teams/${Tenants.PcbWhizApac}") =>
          Response.ok(Right(
            Team(
              domain = Tenants.PcbWhizApac,
              name = "pcbwhiz apac",
              description = ""
            )
          ))

        case r if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizEu}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(r, s"/internal/customer/${Tenants.PcbWhizApac}/find/by-lumiquote/$tenant") =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}/versions/$pcbId/shares"
            ) =>
          assert(r.body.isInstanceOf[StringBody])
          val team = Json.parse(r.body.asInstanceOf[StringBody].s).as[ShareAssemblyTo].team
          Response.ok(Right(
            SharedAssembly(
              team = team,
              id = shareId,
              ref = assemblyReference,
              created = Instant.now,
              information = SharedAssemblyInformation(
                uiStatus = UIStatus.DEFAULT,
                customer = None,
                assignee = None
              )
            )
          ))

        case r if uriMatches(r, s"/internal/assembly/assemblies/$tenant/assemblies/${stubPcb.assemblyId}") =>
          Response.ok(Right(AssemblyWithShares(mkAssembly(pcbId), Seq.empty)))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizEu}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(Seq.empty))

        case r
            if uriMatches(
              r,
              s"/internal/price/teams/${Tenants.PcbWhizApac}/prices/shares/$shareId/specifications/$specificationId/calculate"
            ) =>
          Response.ok(Right(
            Seq(
              CalculatedPrices(
                request =
                  PriceRequest(
                    count = 1,
                    delivery = 1,
                    nre = None,
                    assembly = None,
                    specification = None,
                    panel = None
                  ),
                pricings = Seq(
                  CalculatedPricing(
                    name = "test-pricing-ok",
                    supplier = stackrateOtherTenantSupplier2Id,
                    tables = Seq.empty,
                    price = BigDecimal(10),
                    unitPrice = BigDecimal(1),
                    sumDetails = DetailedSum(
                      flexSum = BigDecimal(10),
                      fixSum = BigDecimal(10),
                      nreSum = None
                    ),
                    currency = CurrencyCode.EUR
                  )
                )
              )
            )
          ))

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizEu}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations/shared-quotations") =>
          Response.ok("")

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotationitems") =>
          Response.ok(Right(QuotationItem(
            id = UUID.randomUUID(),
            assembly = assemblyReference,
            name = Some("some-quotation"),
            description = None,
            info = None
          )))

        case r if uriMatches(r, s"/internal/quotation/teams/${Tenants.PcbWhizApac}/quotations") =>
          Response.ok(Right(Quotation(
            team = tenant,
            assembly = assemblyReference,
            quotationId = UUID.randomUUID(),
            customerId = UUID.randomUUID(),
            name = "quotation-1",
            externalID = None,
            description = None,
            requestID = None,
            requestBy = None,
            created = Instant.now,
            creator = UUID.randomUUID(),
            assignee = None,
            status = QuotationStatus.SENT(Instant.now),
            contactId = None,
            billing = None,
            shipping = None,
            items = Some(Seq()),
            publicNotes = None,
            validFor = None,
            requestDate = None
          )))

        case r
            if uriMatches(
              r,
              s"/internal/ems/panel/$tenant/customerpanels/assemblies/${assemblyReference.id}/versions/${assemblyReference.version}"
            ) =>
          Response.ok(
            Right(Seq[CustomerPanel]())
          )
      }

    val service = mkOfferManagerservice(stubPcb, asyncBackend)

    val request = BatchOfferRequest(
      pcbId = pcbId,
      tenant = tenant,
      api = ManufacturerApi.Stackrate,
      sourcingScenarioRequests = Seq(ScenarioRequest(
        sourcingScenarioId = sourcingScenarioId,
        leadTimes = Seq(LeadTime(LeadTimePreference.Fastest)),
        quantity = 100,
        change = ChangeStatus.New,
        supplierAndStockLocations = Seq(
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationEuropeId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "pcbwhiz",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Europe
          ),
          SupplierAndStockLocationResponse(
            id = supplierAndStockLocationAsiaId,
            supplier = SupplierResponse(
              id = supplierId,
              name = "pcbwhiz",
              supplier_part_type = SupplierPartType.Pcb
            ),
            stock_location = Region.Asia
          )
        )
      )),
      existingOffers = Seq.empty,
      credentials = None,
      panels = Seq(PerPcb(
        pcb = pcbId,
        panelDetails = PanelDetails(
          id = Some(PanelId(UUID.fromString("cdc0dbf5-49a1-4c11-b7a3-0ee60e5edba5"))),
          rowCount = 2,
          columnCount = 2,
          horizontalSpacingInMm = 2,
          verticalSpacingInMm = 2,
          minMillingDistanceInMm = 2,
          padding = LuminovoPadding(2, 2, 2, 2),
          depanelization = Depanelization.VCut,
          pcbIsRotated = false
        )
      )),
      stackratePricingConnections =
        Seq(
          StackratePricingConnection(Tenants.PcbWhizEu, Some(tenant), None),
          StackratePricingConnection(Tenants.PcbWhizApac, Some(tenant), None)
        ),
      panelPreferenceSettings = PanelPreferenceSettings(panelPreferences = None)
    )

    service
      .makeBatchOffers(tenant, request)
      .map { result =>
        result.value.pcbId shouldBe pcbId
        result.value.pcbHash shouldBe Some("test-hash")
        result.value.responses.sortBy(_.location.stockLocation) shouldBe
          Seq(
            ManufacturerStatus(
              api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
              manufacturer = Manufacturer(
                supplierId,
                List(
                  ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
                  ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe)
                ).sortBy(_.stockLocation),
                "pcbwhiz"
              ),
              location = ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
              status = PcbOfferStatus.CustomStackup
            ),
            ManufacturerStatus(
              api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate, None),
              manufacturer = Manufacturer(
                supplierId,
                List(
                  ManufacturerLocation(supplierAndStockLocationAsiaId, Region.Asia),
                  ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe)
                ).sortBy(_.stockLocation),
                "pcbwhiz"
              ),
              location = ManufacturerLocation(supplierAndStockLocationEuropeId, Region.Europe),
              status = PcbOfferStatus.CustomStackup
            )
          ).sortBy(_.location.stockLocation)
      }
  }

  private def mkOfferManagerservice(
      stubPcb: PCB,
      asyncBackend: AsyncBackend
  ): OfferManager = {
    val config = ConfigFactory.parseMap(
      Map(
        "pcb.luminovo.host"            -> "http://backend-core:5000",
        "pcb.luminovo.offer_path"      -> "/api/offers/pcb",
        "pcb.luminovo.ignoreLeadTimes" -> false
      ).asJava
    )
    val pcbServerConfig = PcbServerConfig(config)

    val stubLagomClient = new LagomServiceClient(asyncBackend)

    implicit val assemblyService: AssemblyService     = stubLagomClient.implement[AssemblyService]
    implicit val priceService: PriceService           = stubLagomClient.implement[PriceService]
    implicit val supplierService: SupplierService     = stubLagomClient.implement[SupplierService]
    implicit val quotationService: QuotationService   = stubLagomClient.implement[QuotationService]
    implicit val customerService: CustomerService     = stubLagomClient.implement[CustomerService]
    implicit val panelService: PanelService           = stubLagomClient.implement[PanelService]
    implicit val userService: UserService             = stubLagomClient.implement[UserService]
    implicit val pcbService: PCBService               = stubLagomClient.implement[PCBService]
    implicit val layerstackService: LayerstackService = stubLagomClient.implement[LayerstackService]

    implicit val lang: Lang = Lang.Default

    class TestApi(config: Config)(implicit assemblyService: AssemblyService)
        extends StackRateAPI(
          config,
          asyncBackend,
          assemblyService,
          pcbService,
          layerstackService
        ) {
      override def getPcbNoToken(
          pcbId: PCBId,
          team: StackRateAPI.Team
      )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
        Future.successful(Right(stubPcb))
    }

    new OfferManager(
      pcbServerConfig,
      stackrateApi = new TestApi(config),
      syncBackend = SttpBackendStub.synchronous,
      asyncBackend = asyncBackend
    )
  }

  private def mkAssembly(pcbId: PCBId): Assembly =
    Assembly(
      team = "test-luminovo",
      id = StubPCB.getPcb(pcbId).assemblyId.value,
      gid = "test-gid",
      name = "test-assembly",
      creator = "someone",
      created = Instant.now,
      information = AssemblyInformation(
        customer = None,
        orderId = None,
        assignee = None,
        itemNo = None,
        description = None,
        uiStatus = UIStatus.DEFAULT,
        project = None
      ),
      features = Seq.empty,
      status = AssemblyStatus.INITIAL_STATUS,
      currentVersion = None,
      preview = None,
      mail = None,
      template = None,
      externalReference = None
    )

  private def mkStackrateSupplier(name: String, id: UUID, lqReference: UUID): PCBSupplierDescription =
    PCBSupplierDescription(
      name = name,
      id = Some(id),
      technologies = None,
      region = None,
      lqReference = Some(lqReference)
    )

  private def uriMatches(r: Request[_, _], url: String): Boolean = {
    import cats.syntax.eq._
    if (!url.startsWith("/")) {
      throw new IllegalArgumentException("url must start with /")
    }
    val requestUrl = r.uri.toString.replace("http://", "").dropWhile(_ != '/')

    requestUrl.eqv(url)
  }

}
