package de.fellows.microservices.pcb.model.safePcb

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.{PcbPanelInfo, ScenarioRequestWithPanel}
import de.fellows.luminovo.panel.{Depanelization, ExistingPanel}
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.helper.defaultPcb
import de.fellows.microservices.pcb.model.ApiService
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.lq.{ChangeStatus, LeadTime, LeadTimePreference}
import de.fellows.microservices.pcb.model.panel.{
  EmsPreferences,
  NumberOfPanels,
  PanelGap,
  PanelInfo,
  PanelPadding,
  PanelPreferences,
  PcbMesh,
  Rectangle,
  RequestedPcbs,
  TotalPcbs
}
import de.fellows.microservices.pcb.model.pcb.props.{<PERSON><PERSON>eight, Board<PERSON>idth, ViaFillingType}
import org.scalacheck.Gen
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks
import sttp.client3.testing.SttpBackendStub

import java.util.UUID

class SafePcbApiRequestGroupingTest extends AnyFlatSpec with should.Matchers with ScalaCheckPropertyChecks
    with EitherValues {

  private val stubAsyncBackend           = SttpBackendStub.asynchronousFuture
  private val service: SafePcbApiService = new SafePcbApiService(stubAsyncBackend)(Lang.Default)
  private val panelPreferences: PanelPreferences = PanelPreferences.Empty.copy(
    minWidth = 10,
    minHeight = 10,
    maxWidth = 260,
    maxHeight = 245,
    maxPCBs = None,
    padding = new PanelPadding(5, 5),
    spacing = Rectangle(2, 1)
  )
  private val preferences: EmsPreferences = EmsPreferences.default("test-tenant").copy(
    panelPreferences = Some(panelPreferences)
  )
  private val pcb = defaultPcb.copy(
    properties = defaultPcb.properties.copy(
      basic = defaultPcb.properties.basic.copy(
        boardWidth = BoardWidth(32f),
        boardHeight = BoardHeight(32f)
      ),
      mechanical = defaultPcb.properties.mechanical.copy(
        viaFillingType = ViaFillingType.default
      )
    )
  )

  "fromPcbToSafePcb" should "group requests by panel size" in {
    val scenarios = Seq(
      mkScenarioRequest(100),
      mkScenarioRequest(120),
      mkScenarioRequest(125),
      mkScenarioRequest(150)
    )

    val r = service.fromPcbToSafePcb(
      pcb,
      scenarios,
      preferences
    ).value

    r.size shouldBe 2

    meshSolutions(r).toSet shouldBe Set(
      (
        Right(PcbMesh(5, 5)),
        Seq(
          PanelInfo(
            NumberOfPanels(4),
            RequestedPcbs(100),
            TotalPcbs(100),
            sourcingScenarioId = scenarios.head.sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(5),
            RequestedPcbs(125),
            TotalPcbs(125),
            sourcingScenarioId = scenarios(2).sourcingScenarioId
          )
        )
      ),
      (
        Right(PcbMesh(6, 5)),
        Seq(
          PanelInfo(
            NumberOfPanels(4),
            RequestedPcbs(120),
            TotalPcbs(120),
            sourcingScenarioId = scenarios(1).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(5),
            RequestedPcbs(150),
            TotalPcbs(150),
            sourcingScenarioId = scenarios(3).sourcingScenarioId
          )
        )
      )
    )
  }

  "fromPcbToSafePcb" should "group requests by panel size scenario (mesh does not match scenario)" in {
    val scenarios = Seq(
      mkScenarioRequest(5),
      mkScenarioRequest(10),
      mkScenarioRequest(100)
    )

    val r = service.fromPcbToSafePcb(
      pcb,
      scenarios,
      EmsPreferences.default("test-tenant").copy(panelPreferences =
        Some(PanelPreferences(
          minWidth = 50,
          minHeight = 50,
          maxWidth = 450,
          maxHeight = 400,
          maxPCBs = Some(10),
          padding = new PanelPadding(5, 5),
          spacing = new PanelGap(5, 5),
          depanelization = Depanelization.VCut
        ))
      )
    ).value

//    r.size shouldBe 2

    meshSolutions(r).toSet shouldBe Set(
      (
        Right(PcbMesh(5, 2)),
        Seq(
          PanelInfo(
            NumberOfPanels(1),
            RequestedPcbs(10),
            TotalPcbs(10),
            sourcingScenarioId = scenarios(1).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(10),
            RequestedPcbs(100),
            TotalPcbs(100),
            sourcingScenarioId = scenarios(2).sourcingScenarioId
          )
        )
      ),
      (
        Right(PcbMesh(5, 1)),
        Seq(
          PanelInfo(
            NumberOfPanels(1),
            RequestedPcbs(5),
            TotalPcbs(5),
            sourcingScenarioId = scenarios.head.sourcingScenarioId
          )
        )
      )
    )
  }

  "fromPcbToSafePcb" should "group requests by panel size scenario 2 (mesh does not match scenario)" in {
    val scenarios = Seq(
      mkScenarioRequest(32),
      mkScenarioRequest(33),
      mkScenarioRequest(34)
    )

    val r = service.fromPcbToSafePcb(
      pcb,
      scenarios,
      EmsPreferences.default("test-tenant").copy(panelPreferences =
        Some(PanelPreferences(
          minWidth = 50,
          minHeight = 50,
          maxWidth = 450,
          maxHeight = 400,
          maxPCBs = Some(10),
          padding = new PanelPadding(5, 5),
          spacing = new PanelGap(5, 5),
          depanelization = Depanelization.VCut
        ))
      )
    ).value

    meshSolutions(r).toSet shouldBe Set(
      (
        Right(PcbMesh(2, 1)),
        Seq(
          PanelInfo(
            NumberOfPanels(17),
            RequestedPcbs(34),
            TotalPcbs(34),
            sourcingScenarioId = scenarios(2).sourcingScenarioId
          )
        )
      ),
      (
        Right(PcbMesh(3, 1)),
        Seq(
          PanelInfo(
            NumberOfPanels(11),
            RequestedPcbs(33),
            TotalPcbs(33),
            sourcingScenarioId = scenarios(1).sourcingScenarioId
          )
        )
      ),
      (
        Right(PcbMesh(4, 2)),
        Seq(
          PanelInfo(
            NumberOfPanels(4),
            RequestedPcbs(32),
            TotalPcbs(32),
            sourcingScenarioId = scenarios.head.sourcingScenarioId
          )
        )
      )
    )
  }

  it should "have groups with max 5 panel sizes" in {
    val scenarios = Seq(
      mkScenarioRequest(100),
      mkScenarioRequest(200),
      mkScenarioRequest(300),
      mkScenarioRequest(400),
      mkScenarioRequest(500)
    )

    val r = service.fromPcbToSafePcb(
      pcb,
      scenarios,
      preferences
    ).value

    meshSolutions(r).toSet shouldBe Set(
      (
        Right(PcbMesh(5, 5)),
        List(
          PanelInfo(
            NumberOfPanels(4),
            RequestedPcbs(100),
            TotalPcbs(100),
            sourcingScenarioId = scenarios.head.sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(8),
            RequestedPcbs(200),
            TotalPcbs(200),
            sourcingScenarioId = scenarios(1).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(16),
            RequestedPcbs(400),
            TotalPcbs(400),
            sourcingScenarioId = scenarios(3).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(20),
            RequestedPcbs(500),
            TotalPcbs(500),
            sourcingScenarioId = scenarios(4).sourcingScenarioId
          )
        )
      ),
      (
        Right(PcbMesh(6, 5)),
        List(
          PanelInfo(
            NumberOfPanels(10),
            RequestedPcbs(300),
            TotalPcbs(300),
            sourcingScenarioId = scenarios(2).sourcingScenarioId
          )
        )
      )
    )

    r.size shouldBe 2
  }

  it should "have split into different groups when more than 5 scenarios match the same panel size" in {
    val scenarios = Seq(
      mkScenarioRequest(50),
      mkScenarioRequest(75),
      mkScenarioRequest(100),
      mkScenarioRequest(200),
      mkScenarioRequest(400),
      mkScenarioRequest(500)
    )

    val r = service.fromPcbToSafePcb(
      pcb,
      scenarios,
      preferences
    ).value

    r.size shouldBe 2

    meshSolutions(r).toSet shouldBe Set(
      (
        Right(PcbMesh(5, 5)),
        Seq(
          PanelInfo(
            NumberOfPanels(2),
            RequestedPcbs(50),
            TotalPcbs(50),
            sourcingScenarioId = scenarios.head.sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(3),
            RequestedPcbs(75),
            TotalPcbs(75),
            sourcingScenarioId = scenarios(1).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(4),
            RequestedPcbs(100),
            TotalPcbs(100),
            sourcingScenarioId = scenarios(2).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(8),
            RequestedPcbs(200),
            TotalPcbs(200),
            sourcingScenarioId = scenarios(3).sourcingScenarioId
          ),
          PanelInfo(
            NumberOfPanels(16),
            RequestedPcbs(400),
            TotalPcbs(400),
            sourcingScenarioId = scenarios(4).sourcingScenarioId
          )
        )
      ),
      (
        Right(PcbMesh(5, 5)),
        Seq(
          PanelInfo(
            NumberOfPanels(20),
            RequestedPcbs(500),
            TotalPcbs(500),
            sourcingScenarioId = scenarios(5).sourcingScenarioId
          )
        )
      )
    )
  }

  private val PcbQuantityGenerator: Gen[List[RequestedPcbs]] =
    Gen.nonEmptyListOf(Gen.choose(1, 1000).map(RequestedPcbs.apply))

  "minimize solutions" should "cover all given solutions" in {
    forAll(PcbQuantityGenerator) { (scenarios: Seq[RequestedPcbs]) =>
      val withPanel = withDistributions(scenarios)
      val solutions = SafePcbApiService.trim(withPanel)
      val scenariosFromSolutions = solutions.flatMap {
        case (_, (_, panelQuantity)) => panelQuantity.map(_.requestedPcbs)
      }

      scenariosFromSolutions.sortBy(_.value) should equal(scenarios.sortBy(_.value))
    }
  }
  it should "only provide one mesh for each scenario" in {
    val scenarios = Seq(
      RequestedPcbs(100),
      RequestedPcbs(120),
      RequestedPcbs(125),
      RequestedPcbs(150)
    )

    val withPanel = withDistributions(scenarios)
    val solutions = SafePcbApiService.trim(withPanel)

    solutions should have size 2

    val requestedPcbs = solutions.flatMap {
      case (_, (_, panelQuantity)) => panelQuantity.map(_.requestedPcbs)
    }.sortBy(_.value)
    requestedPcbs shouldBe scenarios
  }

  private def meshSolutions(
      result: Seq[(SafePcbRequest, Seq[PanelInfo])]
  ): Seq[(Either[ExistingPanel, PcbMesh], Seq[PanelInfo])] =
    result.map { p =>
      (
        p._1.calculatedPanelInfo match {
          case p: CalculatedPanelInfo.FromPanelDetails => Right(p.panelDistribution.mesh)
          case p: CalculatedPanelInfo.FromExisting     => Left(p.existing)
        },
        p._2
      )
    }

  private def withDistributions(
      scenarios: Seq[RequestedPcbs]
  ): Seq[(CalculatedPanelInfo, PanelInfo)] =
    scenarios.map { s =>
      val calculatedPanelInfo = ApiService.generateNewPanelFromPreferences(
        s.value,
        pcb.properties.basic.boardWidth.value,
        pcb.properties.basic.boardHeight.value,
        panelPreferences,
        None
      ).value

      (
        calculatedPanelInfo,
        PanelInfo(
          calculatedPanelInfo.numberOfPanels,
          calculatedPanelInfo.requestedPcbs,
          calculatedPanelInfo.totalPcbs,
          sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
        )
      )
    }

  private def mkScenarioRequest(
      quantity: Int
  ): ScenarioRequestWithPanel =
    ScenarioRequestWithPanel(
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID()),
      leadTimes = Seq(LeadTime(LeadTimePreference.Fastest, None)),
      quantity = quantity,
      changeStatus = ChangeStatus.Updated,
      panelInfo = PcbPanelInfo.NoPanel,
      stackratePanel = None,
      manufacturers = Seq.empty
    )
}
