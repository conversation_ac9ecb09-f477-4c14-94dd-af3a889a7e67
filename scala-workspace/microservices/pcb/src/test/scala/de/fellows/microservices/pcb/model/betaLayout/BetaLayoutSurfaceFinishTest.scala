package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.ems.pcb.api.specification.SurfaceFinish.{Enig, HalPbFree, It}
import de.fellows.microservices.pcb.model.pcb.props
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class BetaLayoutSurfaceFinishTest extends AnyFlatSpec with should.Matchers {

  val surfaceFinish = props.SurfaceFinish(None)
  it should "be HAL if PCB value is HalPbFree" in {
    BetaLayoutSurfaceFinish.fromPcb(surfaceFinish.copy(value = HalPbFree)) shouldBe Some(HAL)
  }
  it should "be ENIG if PCB value is Enig" in {
    BetaLayoutSurfaceFinish.fromPcb(surfaceFinish.copy(value = Enig)) shouldBe Some(ENIG)
  }
  it should "be not supported in other PCB cases" in {
    BetaLayoutSurfaceFinish.fromPcb(surfaceFinish.copy(value = It)) shouldBe None
  }
}
