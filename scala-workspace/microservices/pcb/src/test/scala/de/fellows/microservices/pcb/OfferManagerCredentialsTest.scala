package de.fellows.microservices.pcb

import com.osinka.i18n.Lang
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assemby.api._
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.price.api._
import de.fellows.app.quotation.QuotationService
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.microservices.pcb.PcbServer.{AsyncBackend, SyncBackend}
import de.fellows.microservices.pcb.client.LagomServiceClient
import de.fellows.microservices.pcb.model.alba.LoginResponse
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.pcb.ManufacturerApi
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI
import de.fellows.microservices.pcb.model.wurth.WurthApiService.AuthResponse
import de.fellows.microservices.pcb.offers.OfferManager
import de.fellows.utils.security.Auth0TokenContent
import org.scalatest.EitherValues
import org.scalatest.flatspec.AsyncFlatSpec
import org.scalatest.matchers.should
import org.scalatest.prop.TableDrivenPropertyChecks._
import play.api.libs.json.Json
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{Request, Response}

import java.util.Base64
import scala.jdk.CollectionConverters._

class OfferManagerCredentialsTest extends AsyncFlatSpec with should.Matchers with EitherValues {
  "OfferManager" should "check correct service for credentials" in {
    def handleRequest: PartialFunction[Request[_, _], Response[_]] = { request =>
      val uri = request.uri.toString
      uri match {
        case _ if uri.startsWith("http://wuerth:5000") =>
          Response.ok(Right(AuthResponse("some-token")))
        case _ if uri.startsWith("http://beta-layout:5000") =>
          Response.ok(Right(()))
        case _ if uri.startsWith("HTTPS://sales.safe-pcb.com") =>
          Response.ok(Right(()))
        case _ if uri.startsWith("http://ibr:5000") =>
          Response.ok(Right(Json.obj()))
        case _ if uri.startsWith("http://apct:5000") =>
          Response.ok(Right(()))
        case _ if uri.startsWith("http://gatema:5000") =>
          Response.ok(Right(()))
        case _ if uri.startsWith("http://alba:5000") =>
          Response.ok(
            Right(
              LoginResponse(
                success = Some(true),
                hasError = None,
                errors = None,
                token = Some("token"),
                loggedIn = None
              )
            )
          )
      }
    }

    val asyncBackend: AsyncBackend = SttpBackendStub
      .asynchronousFuture
      .whenRequestMatchesPartial(handleRequest)

    val syncBackend: SyncBackend = SttpBackendStub
      .synchronous
      .whenRequestMatchesPartial(handleRequest)

    val service = mkOfferManagerservice(syncBackend, asyncBackend)

    val credentials = Credentials(ManufacturerApi.Wuerth, "12345", "test", region = None)
    val token = Auth0TokenContent(
      sub = "test",
      iss = "test",
      scope = "test",
      azp = "test",
      permissions = Seq.empty,
      tenant = "test",
      analyticsId = "test"
    )
    val encodedToken = new String(Base64.getEncoder.encode(Json.toBytes(Json.toJson(token))))

    val ids = Table(
      ("ids", "expected"),
      (ManufacturerApi.Wuerth, Right(true)),
      (ManufacturerApi.BetaLayout, Right(true)),
      (ManufacturerApi.SafePcb, Right(true)),
      (ManufacturerApi.IBRRingler, Right(true)),
      (ManufacturerApi.APCT, Right(true)),
      (ManufacturerApi.Gatema, Right(true)),
      (ManufacturerApi.Alba, Right(true)),
      (ManufacturerApi.Stackrate, Left("Unhandled manufacturer 'Stackrate'"))
    )

    forAll(ids) { (id, expected) =>
      service.checkCredentials(credentials.copy(manufacturer = id), encodedToken).map { result =>
        result shouldBe expected
      }
    }
  }

  private def mkOfferManagerservice(
      syncBackend: SyncBackend,
      asyncBackend: AsyncBackend
  ): OfferManager = {
    val config = ConfigFactory.parseMap(
      Map(
        "pcb.luminovo.host"            -> "http://backend-core:5000",
        "pcb.luminovo.offer_path"      -> "/api/offers/pcb",
        "pcb.luminovo.ignoreLeadTimes" -> false,
        "wurthapi.host"                -> "http://wuerth:5000",
        "betaLayoutApi.host"           -> "http://beta-layout:5000",
        "betaLayoutApi.key"            -> "test-beta-layout-key",
        "ibrApi.host"                  -> "http://ibr:5000",
        "ibrApi.key"                   -> "test-ibr-key",
        "apct.host"                    -> "http://apct:5000",
        "gatemaApi.host"               -> "http://gatema:5000",
        "gatemaApi.username"           -> "test-gatema-username",
        "gatemaApi.password"           -> "test-gatema-password",
        "gatemaApi.dbProfile"          -> "test-gatema-dbProfile",
        "gatemaApi.serverUrl"          -> "test-gatema-serverUrl",
        "albaApi.host"                 -> "http://alba:5000"
      ).asJava
    )
    val pcbServerConfig = PcbServerConfig(config)

    val stubLagomClient = new LagomServiceClient(asyncBackend)

    implicit val assemblyService: AssemblyService     = stubLagomClient.implement[AssemblyService]
    implicit val pcbService: PCBService               = stubLagomClient.implement[PCBService]
    implicit val priceService: PriceService           = stubLagomClient.implement[PriceService]
    implicit val supplierService: SupplierService     = stubLagomClient.implement[SupplierService]
    implicit val quotationService: QuotationService   = stubLagomClient.implement[QuotationService]
    implicit val customerService: CustomerService     = stubLagomClient.implement[CustomerService]
    implicit val panelService: PanelService           = stubLagomClient.implement[PanelService]
    implicit val userService: UserService             = stubLagomClient.implement[UserService]
    implicit val layerstackService: LayerstackService = stubLagomClient.implement[LayerstackService]

    implicit val lang: Lang = Lang.Default

    class TestApi(config: Config)(implicit assemblyService: AssemblyService)
        extends StackRateAPI(
          config,
          asyncBackend,
          assemblyService,
          pcbService,
          layerstackService
        ) {}

    new OfferManager(
      pcbServerConfig,
      stackrateApi = new TestApi(config),
      syncBackend = syncBackend,
      asyncBackend = asyncBackend
    )
  }

}
