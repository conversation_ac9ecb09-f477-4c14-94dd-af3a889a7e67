package de.fellows.microservices.pcb.model.safePcb

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.{SoldermaskColor, SurfaceFinish}
import de.fellows.microservices.pcb.helper.defaultPcb
import de.fellows.microservices.pcb.model.pcb.props
import de.fellows.microservices.pcb.model.pcb.{PropertyError, PropertyErrorKind}
import de.fellows.microservices.pcb.model.pcb.props.ViaFillingType
import org.scalatest.Assertion
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatest.prop.TableDrivenPropertyChecks._
import de.fellows.microservices.pcb.model.pcb.CustomLabelKind

class SafePcbRequestTest extends AnyFlatSpec with should.Matchers {

  private val pcb = defaultPcb.copy(
    properties = defaultPcb.properties.copy(
      mechanical = defaultPcb.properties.mechanical.copy(
        viaFillingType = ViaFillingType.default
      )
    )
  )

  private implicit val lang: Lang = Lang.Default

  "Validation" should "succeed with valid soldermask colors" in {
    val table = Table(
      ("soldermask color", "safepcb soldermask color"),
      (SoldermaskColor.Green, SafePcbSolderMaskColorGreen),
      (SoldermaskColor.GreenMatt, SafePcbSolderMaskColorGreenMat),
      (SoldermaskColor.Black, SafePcbSolderMaskColorBlack),
      (SoldermaskColor.Red, SafePcbSolderMaskColorRed),
      (SoldermaskColor.Blue, SafePcbSolderMaskColorBlue),
      (SoldermaskColor.Yellow, SafePcbSolderMaskColorYellow),
      (SoldermaskColor.White, SafePcbSolderMaskColorWhite),
      (SoldermaskColor.Purple, SafePcbSolderMaskColorPurple)
    )

    forAll(table) { (solderMaskColor, expected) =>
      val uPcb = pcb.copy(
        properties = pcb.properties.copy(
          basic = pcb.properties.basic.copy(
            soldermaskColor = props.SoldermaskColor(solderMaskColor)
          )
        )
      )
      val rigidPcb = SafePcbRequest.validateAndConvert(uPcb)
      rigidPcb.map(_.solderMaskColor) shouldBe Right(expected)
    }
  }

  it should "succeed with valid surface finishes" in {
    val table = Table(
      ("surface finish", "expected"),
      (SurfaceFinish.HalPbFree, SafePcbHal),
      (SurfaceFinish.It, SafePcbChemicalTin),
      (SurfaceFinish.Is, SafePcbChemicalSilver),
      (SurfaceFinish.Enig, SafePcbEnig),
      (SurfaceFinish.Enepig, SafePcbEnepig),
      (SurfaceFinish.Osp, SafePcbOsp)
    )

    forAll(table) { (surfaceFinish, expected) =>
      val uPcb = pcb.copy(
        properties = pcb.properties.copy(
          basic = pcb.properties.basic.copy(
            surfaceFinish = props.SurfaceFinish(surfaceFinish)
          )
        )
      )
      val rigidPcb = SafePcbRequest.validateAndConvert(uPcb)
      rigidPcb.map(_.surfaceFinish) shouldBe Right(expected)
    }
  }

  it should "fail if hard gold is selected with HalPbFree surface finish" in {
    val uPcb = pcb.copy(
      properties = pcb.properties.copy(
        basic = pcb.properties.basic.copy(
          surfaceFinish = props.SurfaceFinish(SurfaceFinish.HalPbFree),
          hardGold = props.HardGold.yes
        )
      )
    )
    val rigidPcb = SafePcbRequest.validateAndConvert(uPcb)

    val error = PropertyError(
      props.HardGold.yes,
      "Hard Gold is not supported when HAL surface finish is selected",
      PropertyErrorKind.CustomLabel(CustomLabelKind.SafePcbHardGoldError)
    )

    hasError(rigidPcb, error)
  }

  it should "fail if hard gold is selected with HalPb surface finish" in {
    val uPcb = pcb.copy(
      properties = pcb.properties.copy(
        basic = pcb.properties.basic.copy(
          surfaceFinish = props.SurfaceFinish(SurfaceFinish.HalPb),
          hardGold = props.HardGold.yes
        )
      )
    )
    val rigidPcb = SafePcbRequest.validateAndConvert(uPcb)

    val error = PropertyError(
      props.HardGold.yes,
      "Hard Gold is not supported when HAL surface finish is selected",
      PropertyErrorKind.CustomLabel(CustomLabelKind.SafePcbHardGoldError)
    )

    hasError(rigidPcb, error)
  }

  protected def hasError(result: Either[Seq[PropertyError], SafePcbRequest], error: PropertyError): Assertion =
    result.swap.getOrElse(Seq.empty) should contain(error)
}
