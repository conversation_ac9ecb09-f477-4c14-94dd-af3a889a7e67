package de.fellows.microservices.pcb

import com.typesafe.config.ConfigFactory
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.quotation.{Quotation, QuotationService, QuotationStatus}
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.{CustomerPanelAPI, CustomerPanelBoard, PanelService, Spacing}
import de.fellows.luminovo.sourcing._
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.microservices.pcb.client.LagomServiceClient
import de.fellows.microservices.pcb.client.luminovo.LuminovoClientMock
import de.fellows.microservices.pcb.model.pcb.{ManufacturerApi, PCB}
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.Team
import de.fellows.microservices.pcb.model.stackrate.{StackRateAPI, StubLagomApi, StubStackRateAPI}
import de.fellows.microservices.pcb.utils.StubPCB
import de.fellows.utils.model.PCBId
import de.fellows.utils.security.JwtTokenUtil.generateAuthTokenOnly
import de.fellows.utils.security.{Token, TokenContent}
import org.apache.commons.compress.archivers.zip.ZipFile
import org.scalatest.matchers.should
import org.scalatest.wordspec.AsyncWordSpec
import sttp.client3.Response
import sttp.client3.testing.SttpBackendStub

import java.time.Instant
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._
import scala.util.Using

object PanelServiceMock {
  val sharedMockId: UUID = UUID.randomUUID()
  val asyncBackend: AsyncBackend = SttpBackendStub
    .asynchronousFuture
    .whenRequestMatchesPartial {
      case r
          if r.uri.path.contains("customerpanels") =>
        Response.ok(Right(Seq(CustomerPanelAPI(
          id = Some(sharedMockId),
          externalId = Some("test"),
          sourcingScenario = Some(UUID.randomUUID()),
          name = "test",
          elements = Seq(
            CustomerPanelBoard(
              assembly = sharedMockId,
              version = sharedMockId,
              x = 0,
              y = 0,
              angle = None,
              spacing = Spacing(
                topPadding = Some(1),
                rightPadding = Some(1),
                leftPadding = Some(1),
                bottomPadding = Some(1),
                verticalSpacing = Some(1),
                horizontalSpacing = Some(1)
              ),
              repeatX = Some(2),
              repeatY = Some(2),
              multiplier = None
            )
          ),
          description = None,
          preview = None,
          spacing = Spacing(
            topPadding = Some(1),
            rightPadding = Some(1),
            leftPadding = Some(1),
            bottomPadding = Some(1),
            verticalSpacing = Some(1),
            horizontalSpacing = Some(1)
          ),
          working = None,
          selected = None,
          bestYield = None,
          width = Some(100),
          height = Some(100),
          weight = Some(100)
        ))))
    }

  private val executionContext   = ExecutionContext.global
  private val lagomClient        = new LagomServiceClient(asyncBackend)(executionContext)
  val panelService: PanelService = lagomClient.implement[PanelService]

}

object QuotationServiceMock {
  val asyncBackend: AsyncBackend = SttpBackendStub
    .asynchronousFuture
    .whenRequestMatchesPartial {
      case r
          if r.uri.path.contains("quotations") =>
        Response.ok(Right(Seq(Quotation(
          team = "team",
          assembly = AssemblyReference(
            team = "team",
            id = PanelServiceMock.sharedMockId,
            gid = None,
            version = PanelServiceMock.sharedMockId
          ),
          quotationId = PanelServiceMock.sharedMockId,
          customerId = PanelServiceMock.sharedMockId,
          name = "test",
          externalID = None,
          description = None,
          requestID = None,
          requestBy = None,
          created = Instant.now(),
          creator = PanelServiceMock.sharedMockId,
          assignee = None,
          status = QuotationStatus(name = "test", date = None),
          contactId = None,
          billing = None,
          shipping = None,
          items = None,
          publicNotes = None,
          validFor = None,
          requestDate = None
        ))))
    }

  private val executionContext           = ExecutionContext.global
  private val lagomClient                = new LagomServiceClient(asyncBackend)(executionContext)
  val quotationService: QuotationService = lagomClient.implement[QuotationService]
}

class PcbManagerTest extends AsyncWordSpec with should.Matchers {

  private val TOKEN: Token = generateAuthTokenOnly(TokenContent.technicalUser())

  private val asyncBackend                                      = SttpBackendStub.asynchronousFuture
  private val assemblyServiceStub: AssemblyService              = StubLagomApi.assemblyService
  private val pcbServiceStub                                    = StubLagomApi.pcbService
  private implicit val panelServiceStub: PanelService           = PanelServiceMock.panelService
  private implicit val quotationServiceStub: QuotationService   = QuotationServiceMock.quotationService
  private implicit val layerstackServiceStub: LayerstackService = StubLagomApi.layerstackService
  private implicit val luminovoClientMock: LuminovoClientMock   = new LuminovoClientMock
  private implicit val ec: ExecutionContext                     = ExecutionContext.global

  "Get zip" should {
    "return an error if PCB is not available" in {
      val error = NotFoundError("Could not get PCB")
      class TestApi extends StackRateAPI(
            ConfigFactory.empty(),
            asyncBackend,
            assemblyServiceStub,
            pcbServiceStub,
            layerstackServiceStub
          ) {
        override def getPcb(
            pcbId: PCBId,
            token: AuthToken
        )(implicit
            ec: ExecutionContext
        ): Future[Either[ServiceError, PCB]] =
          Future.successful(Left(error))
      }
      val manager = new PcbManager(new TestApi)

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = Seq((SourcingScenarioId(UUID.randomUUID()), 10)),
        manufacturerApi = None,
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map { result =>
        result should be(Left(error))
      }
    }

    "return an error if no scenarios are provided" in {
      val error = InvalidParams("No sourcing scenarios or manufacturers provided")

      class TestApi extends StackRateAPI(
            ConfigFactory.empty(),
            asyncBackend,
            assemblyServiceStub,
            pcbServiceStub,
            layerstackServiceStub
          ) {
        override def getPcb(
            pcbId: PCBId,
            token: AuthToken
        )(implicit
            ec: ExecutionContext
        ): Future[Either[ServiceError, PCB]] =
          Future.successful(Left(error))
      }
      val manager = new PcbManager(new TestApi)

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = Seq(),
        manufacturerApi = None,
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map { result =>
        result should be(Left(error))
      }
    }

    "return a zip file with only PDF specification in it if PCB gerber files are not available" in {
      class TestApi extends StackRateAPI(
            ConfigFactory.empty(),
            asyncBackend,
            assemblyServiceStub,
            pcbServiceStub,
            layerstackServiceStub
          ) {
        override def getPcb(
            pcbId: PCBId,
            token: AuthToken
        )(implicit
            ec: ExecutionContext
        ): Future[Either[ServiceError, PCB]] =
          Future.successful(StubPCB.get(pcbId, token))

        override def getZip(
            pcbId: PCBId,
            token: AuthToken
        )(implicit ec: ExecutionContext): Future[Either[ServiceError, java.io.File]] =
          Future.successful(Left(NotFoundError("Could not get zip")))
      }
      val manager = new PcbManager(new TestApi)

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = Seq((SourcingScenarioId(UUID.randomUUID()), 10)),
        manufacturerApi = None,
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map {
        case Left(error) => fail(error.toString)
        case Right((file, _)) =>
          Using.resource(new ZipFile(file)) { zipFile =>
            zipFile.getEntry("pcb-specification.pdf") should not be null
            succeed
          }
      }
    }
    "return a zip file with added PCB specification" in {
      val config =
        ConfigFactory.parseMap(Map(
          "stackrate.use_pcb_stub" -> true,
          "stackrate.use_zip_stub" -> true
        ).asJava)
      val manager = new PcbManager(StubStackRateAPI.mk(config))

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = Seq((SourcingScenarioId(UUID.randomUUID()), 10)),
        manufacturerApi = None,
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map {
        case Left(error) => fail(error.toString)
        case Right((file, _)) =>
          Using.resource(new ZipFile(file)) { zipFile =>
            zipFile.getEntry("pcb-specification.pdf") should not be null
            zipFile.getEntry("logo_badge_Gerber_1_0.GBL") should not be null
            succeed
          }
      }
    }
    "return a zip file with several added PCB specifications" in {
      val config =
        ConfigFactory.parseMap(Map(
          "stackrate.use_pcb_stub" -> true,
          "stackrate.use_zip_stub" -> true
        ).asJava)
      val manager = new PcbManager(StubStackRateAPI.mk(config))
      val sourcingScenarios = Seq(
        (SourcingScenarioId(UUID.randomUUID()), 10),
        (SourcingScenarioId(UUID.randomUUID()), 100)
      )

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = sourcingScenarios,
        manufacturerApi = Some(ManufacturerApi.Wuerth),
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map {
        case Left(error) => fail(error.toString)
        case Right((file, _)) =>
          Using.resource(new ZipFile(file)) { zipFile =>
            zipFile.getEntry("panel-specification-1x1-10_100-pcbs-for-Wuerth.pdf") should not be null
            succeed
          }
      }
    }

    "returns a zip file if we use Stackrate as manufacturer" in {
      val config =
        ConfigFactory.parseMap(Map(
          "stackrate.use_pcb_stub" -> true,
          "stackrate.use_zip_stub" -> true
        ).asJava)
      val manager = new PcbManager(StubStackRateAPI.mk(config))
      val sourcingScenarios = Seq(
        (SourcingScenarioId(UUID.randomUUID()), 10),
        (SourcingScenarioId(UUID.randomUUID()), 100)
      )

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = sourcingScenarios,
        manufacturerApi = Some(ManufacturerApi.Stackrate),
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map {
        case Left(error) => fail(error.toString)
        case Right((file, _)) =>
          Using.resource(new ZipFile(file)) { zipFile =>
            zipFile.getEntry("panel-specification-1x1-10_100-pcbs.pdf") should not be null
            succeed
          }
      }
    }

    "returns a zip file if the manufacturers have no api" in {
      val config =
        ConfigFactory.parseMap(Map(
          "stackrate.use_pcb_stub" -> true,
          "stackrate.use_zip_stub" -> true
        ).asJava)
      val manager = new PcbManager(StubStackRateAPI.mk(config))
      val sourcingScenarios = Seq(
        (SourcingScenarioId(UUID.randomUUID()), 10),
        (SourcingScenarioId(UUID.randomUUID()), 100)
      )

      manager.getZip(
        pcbId = helper.uuid,
        token = TOKEN.authToken,
        sourcingScenarios = sourcingScenarios,
        manufacturerApi = None,
        includeSpecificationPDF = true,
        includePanelPDF = true
      ).map {
        case Left(error) => fail(error.toString)
        case Right((file, _)) =>
          Using.resource(new ZipFile(file)) { zipFile =>
            zipFile.getEntry("panel-specification-1x1-10_100-pcbs.pdf") should not be null
            succeed
          }
      }
    }
  }

  "updatePcbFileTypes" should {
    "return an error if the pcb does not exist" in {
      val error = NotFoundError("Could not get PCB")
      class TestApi extends StackRateAPI(
            ConfigFactory.empty(),
            asyncBackend,
            assemblyServiceStub,
            pcbServiceStub,
            layerstackServiceStub
          ) {
        override def getPcb(
            pcbId: PCBId,
            token: AuthToken
        )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
          Future.successful(Left(error))
      }
      val manager = new PcbManagerTestOverride(new TestApi, PcbServerConfig(ConfigFactory.empty()))
      manager.updatePcbFileTypes(helper.uuid, TOKEN.authToken, Seq()).map { response =>
        response should be(Left(error))
      }
    }

    "return an error if it's not possible to get the team from the token" in {
      val error = ServerError("couldn't decode")
      val api   = StubStackRateAPI.mk
      val manager = new PcbManager(api) {
        override protected def tokenFromTeam(token: String): Either[ServiceError, Team] =
          Left(ServerError("couldn't decode"))
      }
      manager.updatePcbFileTypes(helper.uuid, TOKEN.authToken, Seq()).map { response =>
        response should be(Left(error))
      }
    }

    "returns stubbed response" in {
      val config =
        ConfigFactory.parseMap(Map(
          "stackrate.use_pcb_stub" -> true,
          "stackrate.use_zip_stub" -> true
        ).asJava)
      val manager = new PcbManagerTestOverride(StubStackRateAPI.mk(config), PcbServerConfig(config))
      manager.updatePcbFileTypes(helper.uuid, TOKEN.authToken, Seq()).map { response =>
        response should be(Right(()))
      }
    }
  }

  private class PcbManagerTestOverride(api: StackRateAPI, config: PcbServerConfig) extends PcbManager(api) {
    override protected def tokenFromTeam(token: String): Either[ServiceError, Team] =
      Right(Team("test-team"))
  }
}
