package de.luminovo.odb.odbpp.model.constants

import de.luminovo.odb.odbpp.model.validation.ODBParseError

sealed trait BarcodeAscii {
  def odbString = this match {
    case BarcodeFullAscii    => "Y"
    case BarcodePartialAscii => "N"
  }
}

object BarcodeAscii {
  def apply(s: String): BarcodeAscii = s match {
    case "Y" => BarcodeFullAscii
    case "N" => BarcodePartialAscii
    case _   => throw new ODBParseError(s"Invalid barcode ascii: $s")
  }
}

case object BarcodeFullAscii    extends BarcodeAscii
case object BarcodePartialAscii extends BarcodeAscii

sealed trait BarcodeChecksum {
  def odbString = this match {
    case BarcodeWithChecksum    => "Y"
    case BarcodeWithoutChecksum => "N"
  }
}

object BarcodeChecksum {
  def apply(s: String): BarcodeChecksum = s match {
    case "Y" => BarcodeWithChecksum
    case "N" => BarcodeWithoutChecksum
    case _   => throw new ODBParseError(s"Invalid barcode checksum: $s")
  }
}

case object Barcode<PERSON><PERSON><PERSON><PERSON><PERSON>um    extends BarcodeChecksum
case object BarcodeWithoutChecksum extends BarcodeChecksum

sealed trait BarcodeBackground {
  def odbString = this match {
    case BarcodeInvertedBackground => "Y"
    case BarcodeNoBackground       => "N"
  }
}

object BarcodeBackground {
  def apply(s: String): BarcodeBackground = s match {
    case "Y" => BarcodeInvertedBackground
    case "N" => BarcodeNoBackground
    case _   => throw new ODBParseError(s"Invalid barcode background: $s")
  }
}

case object BarcodeInvertedBackground extends BarcodeBackground
case object BarcodeNoBackground       extends BarcodeBackground

sealed trait BarcodeTextOption {
  def odbString = this match {
    case BarcodeWithText    => "Y"
    case BarcodeWithoutText => "N"
  }
}

object BarcodeTextOption {
  def apply(s: String): BarcodeTextOption = s match {
    case "Y" => BarcodeWithText
    case "N" => BarcodeWithoutText
    case _   => throw new ODBParseError(s"Invalid barcode text option: $s")
  }
}

case object BarcodeWithText    extends BarcodeTextOption
case object BarcodeWithoutText extends BarcodeTextOption

sealed trait BarcodeTextPosition {
  def odbString = this match {
    case BarcodeTextTop    => "T"
    case BarcodeTextBottom => "B"
  }
}

object BarcodeTextPosition {
  def apply(s: String): BarcodeTextPosition = s match {
    case "T" => BarcodeTextTop
    case "B" => BarcodeTextBottom
    case _   => throw new ODBParseError(s"Invalid barcode text position: $s")
  }
}

case object BarcodeTextTop    extends BarcodeTextPosition
case object BarcodeTextBottom extends BarcodeTextPosition
